<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddIndexesForSalesAnalytics extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // إضافة فهارس لجدول pos لتحسين الأداء
        Schema::table('pos', function (Blueprint $table) {
            // فهرس مركب للاستعلامات السريعة
            $table->index(['created_by', 'pos_date'], 'idx_pos_creator_date');
            $table->index(['warehouse_id', 'pos_date'], 'idx_pos_warehouse_date');
            $table->index(['created_by', 'warehouse_id', 'pos_date'], 'idx_pos_creator_warehouse_date');
            $table->index('created_at', 'idx_pos_created_at');
        });

        // إضافة فهارس لجدول pos_payments
        Schema::table('pos_payments', function (Blueprint $table) {
            $table->index('pos_id', 'idx_pos_payments_pos_id');
            $table->index('amount', 'idx_pos_payments_amount');
        });

        // إضافة فهارس لجدول pos_v2 إذا كان موجود
        if (Schema::hasTable('pos_v2')) {
            Schema::table('pos_v2', function (Blueprint $table) {
                $table->index(['created_by', 'pos_date'], 'idx_pos_v2_creator_date');
                $table->index(['warehouse_id', 'pos_date'], 'idx_pos_v2_warehouse_date');
                $table->index(['created_by', 'warehouse_id', 'pos_date'], 'idx_pos_v2_creator_warehouse_date');
                $table->index('created_at', 'idx_pos_v2_created_at');
            });
        }

        // إضافة فهارس لجدول pos_v2_payments إذا كان موجود
        if (Schema::hasTable('pos_v2_payments')) {
            Schema::table('pos_v2_payments', function (Blueprint $table) {
                $table->index('pos_id', 'idx_pos_v2_payments_pos_id');
                $table->index('amount', 'idx_pos_v2_payments_amount');
            });
        }

        // إضافة فهارس لجدول users
        Schema::table('users', function (Blueprint $table) {
            $table->index('type', 'idx_users_type');
            $table->index(['id', 'type'], 'idx_users_id_type');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // حذف الفهارس
        Schema::table('pos', function (Blueprint $table) {
            $table->dropIndex('idx_pos_creator_date');
            $table->dropIndex('idx_pos_warehouse_date');
            $table->dropIndex('idx_pos_creator_warehouse_date');
            $table->dropIndex('idx_pos_created_at');
        });

        Schema::table('pos_payments', function (Blueprint $table) {
            $table->dropIndex('idx_pos_payments_pos_id');
            $table->dropIndex('idx_pos_payments_amount');
        });

        if (Schema::hasTable('pos_v2')) {
            Schema::table('pos_v2', function (Blueprint $table) {
                $table->dropIndex('idx_pos_v2_creator_date');
                $table->dropIndex('idx_pos_v2_warehouse_date');
                $table->dropIndex('idx_pos_v2_creator_warehouse_date');
                $table->dropIndex('idx_pos_v2_created_at');
            });
        }

        if (Schema::hasTable('pos_v2_payments')) {
            Schema::table('pos_v2_payments', function (Blueprint $table) {
                $table->dropIndex('idx_pos_v2_payments_pos_id');
                $table->dropIndex('idx_pos_v2_payments_amount');
            });
        }

        Schema::table('users', function (Blueprint $table) {
            $table->dropIndex('idx_users_type');
            $table->dropIndex('idx_users_id_type');
        });
    }
}
