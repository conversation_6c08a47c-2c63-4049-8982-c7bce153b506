<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('product_expiry_dates')) {
            Schema::create('product_expiry_dates', function (Blueprint $table) {
                $table->id();
                $table->unsignedBigInteger('product_id');
                $table->unsignedBigInteger('warehouse_id');
                $table->date('expiry_date')->nullable();
                $table->integer('created_by');
                $table->timestamps();

                // Índices y claves foráneas
                $table->foreign('product_id')->references('id')->on('product_services')->onDelete('cascade');
                $table->foreign('warehouse_id')->references('id')->on('warehouses')->onDelete('cascade');

                // Índice único para evitar duplicados
                $table->unique(['product_id', 'warehouse_id']);
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_expiry_dates');
    }
};
