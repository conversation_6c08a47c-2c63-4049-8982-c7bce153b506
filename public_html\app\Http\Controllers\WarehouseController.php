<?php

namespace App\Http\Controllers;

use App\Models\Customer;
use App\Models\Pos;
use App\Models\ProductService;
use App\Models\Purchase;
use App\Models\Utility;
use App\Models\Vender;
use App\Models\warehouse;
use App\Models\WarehouseProduct;
use DB;
use Illuminate\Http\Request;

class WarehouseController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {

        $warehouses = warehouse::where('created_by', '=', \Auth::user()->creatorId())->get();

        return view('warehouse.index', compact('warehouses'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        return view('warehouse.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        if (\Auth::user()->can('create warehouse')) {
            $validator = \Validator::make(
                $request->all(),
                [
                    'name' => 'required',
                    'address' => 'required',
                    'city' => 'required',
                    'city_zip' => 'required', // جعل حقل الرمز البريدي مطلوبًا
                ]
            );
            if ($validator->fails()) {
                $messages = $validator->getMessageBag();

                return redirect()->back()->with('error', $messages->first());
            }

            $warehouse             = new warehouse();
            $warehouse->name       = $request->name;
            $warehouse->address    = $request->address;
            $warehouse->city       = $request->city;
            $warehouse->city_zip   = $request->city_zip;
            $warehouse->created_by = \Auth::user()->creatorId();
            $warehouse->save();

            return redirect()->route('warehouse.index')->with('success', __('Warehouse successfully created.'));
        } else {
            return redirect()->back()->with('error', __('Permission denied.'));
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\warehouse  $warehouse
     * @return \Illuminate\Http\Response
     */
    public function show(warehouse $warehouse)
    {
        if (\Auth::user()->can('show warehouse')) {
            // Previous method commented for reference
            /*
            // Get all products in this warehouse
            $warehouseProducts = WarehouseProduct::where('warehouse_id', $warehouse->id)
                                              ->with(['product'])
                                              ->get();

            // Get product IDs
            $productIds = $warehouseProducts->pluck('product_id')->toArray();

            // Get full product details
            $products = ProductService::whereIn('id', $productIds)->get();

            // Map quantities to products
            foreach ($products as $product) {
                $product->warehouse_quantity = $product->warehouseProduct($product->id, $warehouse->id);
            }
            */

            // New improved method using join to get products with quantities
            $products = ProductService::join('warehouse_products', 'product_services.id', '=', 'warehouse_products.product_id')
                ->where('warehouse_products.warehouse_id', $warehouse->id)
                ->select(
                    'product_services.*',
                    'warehouse_products.quantity as warehouse_quantity',
                    'warehouse_products.id as warehouse_product_id'
                )
                ->with(['category', 'unit']) // Eager load relationships
                ->get();

            // Log for debugging
            \Log::info('Warehouse products found: ' . $products->count());
            foreach ($products as $product) {
                \Log::info("Product: {$product->name}, Quantity: {$product->warehouse_quantity}");
            }

            return view('warehouse.show', compact('products', 'warehouse'));
        } else {
            return redirect()->back()->with('error', __('Permission denied.'));
        }
    }
    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\warehouse  $warehouse
     * @return \Illuminate\Http\Response
     */
    public function edit(warehouse $warehouse)
    {

        if (\Auth::user()->can('edit warehouse')) {
            if ($warehouse->created_by == \Auth::user()->creatorId()) {
                return view('warehouse.edit', compact('warehouse'));
            } else {
                return response()->json(['error' => __('Permission denied.')], 401);
            }
        } else {
            return response()->json(['error' => __('Permission denied.')], 401);
        }
    }
    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\warehouse  $warehouse
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, warehouse $warehouse)
    {

        if (\Auth::user()->can('edit warehouse')) {
            if ($warehouse->created_by == \Auth::user()->creatorId()) {
                $validator = \Validator::make(
                    $request->all(),
                    [
                        'name' => 'required',
                        'address' => 'required',
                        'city' => 'required',
                        'city_zip' => 'required', // جعل حقل الرمز البريدي مطلوبًا
                    ]
                );
                if ($validator->fails()) {
                    $messages = $validator->getMessageBag();

                    return redirect()->back()->with('error', $messages->first());
                }

                $warehouse->name       = $request->name;
                $warehouse->address    = $request->address;
                $warehouse->city       = $request->city;
                $warehouse->city_zip   = $request->city_zip;
                $warehouse->save();

                return redirect()->route('warehouse.index')->with('success', __('Warehouse successfully updated.'));
            } else {
                return redirect()->back()->with('error', __('Permission denied.'));
            }
        } else {
            return redirect()->back()->with('error', __('Permission denied.'));
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\warehouse  $warehouse
     * @return \Illuminate\Http\Response
     */
    public function destroy(warehouse $warehouse)
    {
        if (\Auth::user()->can('delete warehouse')) {
            if ($warehouse->created_by == \Auth::user()->creatorId()) {
                $warehouse->delete();


                return redirect()->route('warehouse.index')->with('success', __('Warehouse successfully deleted.'));
            } else {
                return redirect()->back()->with('error', __('Permission denied.'));
            }
        } else {
            return redirect()->back()->with('error', __('Permission denied.'));
        }
    }


    //get the customer pos for warehouse
    public function customerPosBills(int $customer_id, Request $request)
    {
        $warehouse_id = $request->query('warehouse');
        $warehouse = warehouse::find($warehouse_id);
        $customer = Customer::find($customer_id);

        //get all pos records if -uncoment if client wants that- B.B
        // $posPayments = Pos::where('customer_id', '=', $customer_id)->where('warehouse_id', '=', $warehouse_id)->with(['customer', 'warehouse'])->get();

        //get all for closed shift
        $posPayments = Pos::where('customer_id', '=', $customer_id)
            ->where('warehouse_id', '=', $warehouse_id)
            ->where(function ($query) {
                $query->whereHas('shift', function ($subQuery) {
                    $subQuery->where('is_closed', true);
                })->orWhereNull('shift_id');
            })
            ->with(['customer', 'warehouse'])
            ->get();

        // dd($posPayments);

        return view('warehouse.customer_pos_bills', compact('posPayments', 'customer'));
    }

    //get the vendor purchase pos for warehouse
    public function vendorPosBills(int $vender_id, Request $request)
    {
        $warehouse_id = $request->query('warehouse');
        $warehouse = warehouse::find($warehouse_id);
        $vendor = Vender::find($vender_id);

        //get all purchases if -uncoment if client wants that- B.B
        // $purchases = Purchase::where('vender_id', '=', $vender_id)->where('warehouse_id', '=', $warehouse_id)->with(['vender', 'category'])->get();

        $purchases = Purchase::where('vender_id', '=', $vender_id)
            ->where('warehouse_id', '=', $warehouse_id)
            ->where(function ($query) {
                $query->whereHas('shift', function ($subQuery) {
                    $subQuery->where('is_closed', true);
                })->orWhereNull('shift_id');
            })->with(['vender', 'category'])->get();

        // dd($posPayments);

        return view('warehouse.vendor_pos_bills', compact('purchases', 'vendor'));
    }

    /**
     * Get warehouse products data via AJAX
     *
     * @param  \App\Models\warehouse  $warehouse
     * @return \Illuminate\Http\Response
     */
    public function getWarehouseProducts(warehouse $warehouse)
    {
        try {
            if (!\Auth::user()->can('show warehouse')) {
                return response()->json([
                    'success' => false,
                    'message' => __('Permission denied.')
                ], 403);
            }

            if ($warehouse->created_by != \Auth::user()->creatorId()) {
                return response()->json([
                    'success' => false,
                    'message' => __('You can only view your own warehouses.')
                ], 403);
            }

            // Get all products in this warehouse with their quantities
            $warehouseProducts = WarehouseProduct::where('warehouse_id', $warehouse->id)
                                              ->with(['product'])
                                              ->get();

            // Log for debugging
            \Log::info('Warehouse products found: ' . $warehouseProducts->count());

            // Get product IDs
            $productIds = $warehouseProducts->pluck('product_id')->toArray();

            // Get full product details
            $products = ProductService::whereIn('id', $productIds)->get();

            // Log for debugging
            \Log::info('Products found: ' . $products->count());

            // Map quantities to products - use the actual quantity from WarehouseProduct
            $productsData = [];
            foreach ($warehouseProducts as $warehouseProduct) {
                if ($warehouseProduct->product) {
                    $product = $warehouseProduct->product;
                    $taxData = [];

                    if (!empty($product->tax_id)) {
                        $taxes = \Utility::tax($product->tax_id);
                        foreach ($taxes as $tax) {
                            $taxData[] = [
                                'name' => $tax->name,
                                'rate' => $tax->rate
                            ];
                        }
                    }

                    // Registrar para depuración
                    \Log::info('Product in warehouse: ID=' . $warehouseProduct->product_id . ', Name=' . $product->name . ', Quantity=' . $warehouseProduct->quantity);

                    $productsData[] = [
                        'id' => $warehouseProduct->product_id,
                        'name' => $product->name,
                        'sku' => $product->sku,
                        'sale_price' => \Auth::user()->priceFormat($product->sale_price),
                        'purchase_price' => \Auth::user()->priceFormat($product->purchase_price),
                        'quantity' => $warehouseProduct->quantity,
                        'warehouse_quantity' => $warehouseProduct->quantity,
                        'category' => !empty($product->category) ? $product->category->name : '-',
                        'tax' => $taxData,
                        'unit' => !empty($product->unit) ? $product->unit->name : '-'
                    ];
                }
            }

            // Log for debugging
            \Log::info('Products data prepared: ' . count($productsData));

            return response()->json([
                'success' => true,
                'products' => $productsData,
                'timestamp' => now()->format('Y-m-d H:i:s')
            ]);
        } catch (\Exception $e) {
            \Log::error('Error in getWarehouseProducts: ' . $e->getMessage());
            \Log::error($e->getTraceAsString());

            return response()->json([
                'success' => false,
                'message' => __('An error occurred while fetching warehouse data.'),
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
