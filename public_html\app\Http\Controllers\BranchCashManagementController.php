<?php

namespace App\Http\Controllers;

use App\Models\FinancialRecord;
use App\Models\Shift;
use App\Models\User;
use App\Models\warehouse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class BranchCashManagementController extends Controller
{
    /**
     * Display a listing of all shifts and their financial records.
     *
     * @return \Illuminate\View\View
     */
    public function index(Request $request)
    {
        // Get filter parameters
        $status = $request->input('status', 'all'); // 'all', 'open', 'closed'
        $warehouse_id = $request->input('warehouse_id');
        $start_date = $request->input('start_date');
        $end_date = $request->input('end_date');

        // Base query for shifts with financial records
        $query = Shift::with(['financialRecord', 'creator', 'closer'])
            ->join('warehouses', 'shifts.warehouse_id', '=', 'warehouses.id')
            ->select('shifts.*', 'warehouses.name as warehouse_name');

        // Apply filters
        if ($status === 'open') {
            $query->where('shifts.is_closed', false);
        } elseif ($status === 'closed') {
            $query->where('shifts.is_closed', true);
        }

        if ($warehouse_id) {
            $query->where('shifts.warehouse_id', $warehouse_id);
        }

        if ($start_date) {
            $query->whereDate('shifts.created_at', '>=', $start_date);
        }

        if ($end_date) {
            $query->whereDate('shifts.created_at', '<=', $end_date);
        }

        // Get shifts with pagination
        $shifts = $query->orderBy('shifts.created_at', 'desc')->paginate(10);

        // Get warehouses for filter dropdown
        $warehouses = warehouse::where('created_by', Auth::user()->creatorId())->get();

        return view('company_operations.branch_cash_management.index', compact('shifts', 'warehouses', 'status', 'warehouse_id', 'start_date', 'end_date'));
    }

    /**
     * Show the details of a specific shift and its financial record.
     *
     * @param int $id
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $shift = Shift::with(['financialRecord', 'creator', 'closer'])->findOrFail($id);

        return view('company_operations.branch_cash_management.show', compact('shift'));
    }

    /**
     * Update a financial record.
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateFinancialRecord(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'field' => 'required|string',
            'value' => 'required|numeric|min:0',
        ]);

        if ($validator->fails()) {
            return response()->json(['success' => false, 'message' => $validator->errors()->first()], 400);
        }

        try {
            DB::beginTransaction();

            $financialRecord = FinancialRecord::findOrFail($id);
            $field = $request->input('field');
            $value = $request->input('value');

            // Validate that the field is allowed to be updated
            $allowedFields = [
                'opening_balance',
                'current_cash',
                'overnetwork_cash',
                'delivery_cash',
                'deficit',
                'received_advance'
            ];

            if (!in_array($field, $allowedFields)) {
                return response()->json(['success' => false, 'message' => __('Field cannot be updated')], 400);
            }

            // Update the field
            $financialRecord->$field = $value;

            // Recalculate total_cash (sum of current_cash and overnetwork_cash only)
            $financialRecord->total_cash = $financialRecord->current_cash + $financialRecord->overnetwork_cash;

            // Update the updater
            $financialRecord->updated_by = Auth::id();
            $financialRecord->save();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => __('Financial record updated successfully'),
                'data' => [
                    'id' => $financialRecord->id,
                    'field' => $field,
                    'value' => $value,
                    'total_cash' => $financialRecord->total_cash,
                    'updated_by' => Auth::user()->name,
                    'updated_at' => $financialRecord->updated_at->format('Y-m-d H:i:s')
                ]
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Close a shift for a user.
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function closeShift(Request $request, $id)
    {
        try {
            DB::beginTransaction();

            // Find the shift
            $shift = Shift::findOrFail($id);

            // Check if shift is already closed
            if ($shift->is_closed) {
                return response()->json(['success' => false, 'message' => __('Shift is already closed')], 400);
            }

            // Close the shift
            $shift->is_closed = true;
            $shift->closed_at = now();
            $shift->closed_by = Auth::id();
            $shift->save();

            // Update the user's session status
            $user = User::find($shift->created_by);
            if ($user) {
                $user->is_sale_session_new = true;
                $user->save();
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => __('Shift closed successfully'),
                'data' => [
                    'id' => $shift->id,
                    'is_closed' => true,
                    'closed_at' => $shift->closed_at->format('Y-m-d H:i:s'),
                    'closed_by' => Auth::user()->name
                ]
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Reopen a shift for a user.
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function reopenShift(Request $request, $id)
    {
        try {
            DB::beginTransaction();

            // Find the shift
            $shift = Shift::findOrFail($id);

            // Check if shift is already open
            if (!$shift->is_closed) {
                return response()->json(['success' => false, 'message' => __('Shift is already open')], 400);
            }

            // Reopen the shift
            $shift->is_closed = false;
            $shift->closed_at = null;
            $shift->closed_by = null;
            $shift->save();

            // Update the user's session status
            $user = User::find($shift->created_by);
            if ($user) {
                $user->is_sale_session_new = false;
                $user->save();
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => __('Shift reopened successfully'),
                'data' => [
                    'id' => $shift->id,
                    'is_closed' => false,
                    'updated_by' => Auth::user()->name
                ]
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }
}
