<?php

namespace Database\Seeders;

use App\Models\Template;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class AiTemplateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $template = [
//            [
//                'template_name'=>'grammar',
//                'prompt'=>"please correct grammar mistakes and spelling mistakes in this: '###description##'",
//                'module'=>'grammar',
//                'field_json'=>'',
//                'is_tone'=>'1',
//                "created_at" => date('Y-m-d H:i:s'),
//                "updated_at" => date('Y-m-d H:i:s'),
//            ],
            [
                'template_name'=>'leave_reason',
                'prompt'=>"Generate a comma-separated string of common leave reasons that employees may provide to their employers. Include both personal and professional reasons for taking leave, such only ##type## . Aim to generate a diverse range of leave reasons that can be used in different situations. Please provide a comprehensive and varied list of leave reasons that can help employers understand and accommodate their employees' needs.",
                'module'=>'leave',
                'field_json'=>'{"field":[{"label":"Leave Type","placeholder":"e.g.illness, family emergencies,vacation","field_type":"text_box","field_name":"type"}]}',
                'is_tone'=>'1',
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
            [
                'template_name'=>'subject',
                'prompt'=>"Generate a goal subject for an employee's goal related type to ##type##.",
                'module'=>'goal tracking',
                'field_json'=>'{"field":[{"label":"Goal Type","placeholder":"e.g.invoice, production,hiring","field_type":"text_box","field_name":"type"}]}',
                'is_tone'=>'0',
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
            [
                'template_name'=>'description',
                'prompt'=>"Generate a goal descriptions for an employee's goal title is ##title##.",
                'module'=>'goal tracking',
                'field_json'=>'{"field":[{"label":"Goal Title","placeholder":"e.g.Invoice Accuracy","field_type":"text_box","field_name":"title"}]}',
                'is_tone'=>'1',
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
            [
                'template_name'=>'description',
                'prompt'=>"Generate a job training descriptions for a ##position## position. The training description should include responsibilities such as ##responsibilities##. Please ensure the descriptions are concise, informative, and accurately reflect the key responsibilities of a ##position##.",
                'module'=>'training',
                'field_json'=>'{"field":[{"label":"Position","placeholder":"job training descriptions","field_type":"text_box","field_name":"position"},{"label":"Responsibilities","placeholder":"Managing training logistics","field_type":"textarea","field_name":"responsibilities"}]}',
                'is_tone'=>'1',
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
            [
                'template_name'=>'title',
                'prompt'=>"Generate a list of job titles commonly found in an ##work_place##. The job titles should cover a range of roles and responsibilities within the field of ##field##. Include positions such as ##positions##. Please provide a diverse selection of job titles that accurately reflect the various positions found in an ##work_place##.",
                'module'=>'job',
                'field_json'=>'{"field":[{"label":"Work Place","placeholder":"e.g.IT Company,hospital","field_type":"text_box","field_name":"work_place"},{"label":"Field ","placeholder":"e.g.Backend","field_type":"text_box","field_name":"field"},{"label":"Positions","placeholder":"e.g.developer,tester","field_type":"text_box","field_name":"positions"}]}',
                'is_tone'=>'0',
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
            [
                'template_name'=>'description',
                'prompt'=>"Generate a job descriptions for a ##position## position. The job description should include responsibilities such as ##responsibilities##. Please ensure the descriptions are concise, informative, and accurately reflect the key responsibilities of a ##position##.",
                'module'=>'job',
                'field_json'=>'{"field":[{"label":"Position","placeholder":"job for a position","field_type":"text_box","field_name":"position"},{"label":"Responsibilities","placeholder":"","field_type":"textarea","field_name":"responsibilities"}]}',
                'is_tone'=>'1',
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
            [
                'template_name'=>'requirement',
                'prompt'=>"Generate a comma-separated string of job requirements for a ##position## position. The requirements should include ##description##. Please provide the requirements in a comma-separated string format.",
                'module'=>'job',
                'field_json'=>'{"field":[{"label":"Position","placeholder":"requirement of job","field_type":"text_box","field_name":"position"},{"label":"Description","placeholder":"","field_type":"textarea","field_name":"description"}]}',
                'is_tone'=>'1',
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
            [
                'template_name'=>'description',
                'prompt'=>"Generate a description for presenting the Award. The description should highlight ##reasons##. Emphasize the significance of the  Award as a symbol of recognition for employee's remarkable accomplishments and its representation of her '##reasons##' and impact on the organization. Please create a personalized and engaging description that conveys appreciation, pride, and gratitude for employee's contributions to the company's sucess",
                'module'=>'award',
                'field_json'=>'{"field":[{"label":"Award reasons","placeholder":"e.g.skilled, focused ,efficiency","field_type":"textarea","field_name":"reasons"}]}',
                'is_tone'=>'1',
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
            [
                'template_name'=>'description',
                'prompt'=>"Generate a list of common reasons for employee transfers within an organization. Include reasons such as ##reasons##. Please provide a comprehensive and varied list of reasons that can help employers understand and address employee transfer situations effectively.",
                'module'=>'transfer',
                'field_json'=>'{"field":[{"label":"Transfer reasons","placeholder":"e.g.career development,special projects or initiatives","field_type":"textarea","field_name":"reasons"}]}',
                'is_tone'=>'1',
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
            [
                'template_name'=>'description',
                'prompt'=>"Generate a description why an employee might choose to resign and request a transfer to another location within the company. Include both professional and personal reasons that could contribute to this decision. Examples may include ##reasons##. Aim to provide a comprehensive and varied description that can help employers understand and accommodate employees' needs when considering a transfer request",
                'module'=>'resignation',
                'field_json'=>'{"field":[{"label":"Resignation reasons","placeholder":"e.g.career development,health issues","field_type":"textarea","field_name":"reasons"}]}',
                'is_tone'=>'1',
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
            [
                'template_name'=>'description',
                'prompt'=>"Generate a description for organizing a company trip. The trip aims to ##aims## . Please provide a diverse description that highlight the benefits and positive outcomes associated with organizing a company trip. Focus on creating an engaging and enjoyable experience for employees while also achieving business objectives and cultivating a positive work environment.",
                'module'=>'travel',
                'field_json'=>'{"field":[{"label":"Aims","placeholder":"e.g.career development,health issues","field_type":"textarea","field_name":"aims"}]}',
                'is_tone'=>'1',
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
            [
                'template_name'=>'promotion_title',
                'prompt'=>"Generate a list of promotion title suggestions for an ##role##. The promotion titles should reflect ##reasons##, and recognition of the ##role##'s accomplishments. Please provide a diverse range of promotion titles that align with ##role## job roles and levels within the company. Aim to create titles that are both professional and descriptive, highlighting the ##role##'s progression and impact within the organization.",
                'module'=>'promotion',
                'field_json'=>'{"field":[{"label":"Job","placeholder":"e.g.doctor, developer","field_type":"text_box","field_name":"role"},{"label":"Promotion Reasons","placeholder":"e.g.increased responsibility, higher position","field_type":"textarea","field_name":"reasons"}]}',
                'is_tone'=>'0',
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
            [
                'template_name'=>'description',
                'prompt'=>"Generate a promotion description for this title:##title##. ",
                'module'=>'promotion',
                'field_json'=>'{"field":[{"label":"Promotion Title","placeholder":"e.g.Medical Director","field_type":"text_box","field_name":"title"}]}',
                'is_tone'=>'1',
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
            [
                'template_name'=>'title',
                'prompt'=>"Generate a list of titles for complaints related to employee and company issues. ##reasons##. Please provide a range of titles that accurately reflect common complaint categories, ensuring they are concise, descriptive, and effective in conveying the nature of the complaint. ",
                'module'=>'complaint',
                'field_json'=>'{"field":[{"label":"Complaint reasons","placeholder":"e.g.unprofessional behavior, harassment,","field_type":"textarea","field_name":"reasons"}]}',
                'is_tone'=>'0',
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
            [
                'template_name'=>'description',
                'prompt'=>"Generate a Complaint description for this title:##title##. ",
                'module'=>'complaint',
                'field_json'=>'{"field":[{"label":"Complaint Title","placeholder":"e.g.Unprofessional Behavior Complaint","field_type":"text_box","field_name":"title"}]}',
                'is_tone'=>'1',
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
            [
                'template_name'=>'description',
                'prompt'=>"Generate a warning description for an employee who consistently ##reasons##. The warning should address the employee's ##reasons##, including further disciplinary action or termination of employment. Please provide a clear and firm warning message that encourages the employee to review the policy and make immediate improvements.",
                'module'=>'warning',
                'field_json'=>'{"field":[{"label":"Warning reasons","placeholder":"e.g.break attendance policy","field_type":"textarea","field_name":"reasons"}]}',
                'is_tone'=>'1',
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
            [
                'template_name'=>'description',
                'prompt'=>"Generate a termination description for  the reason :##reason##. The description should convey the company's regret over the decision and outline the specific concerns, such as ##reasons##. Please provide a clear and professional message that explains the decision while expressing appreciation for the employee's contributions. Aim to offer guidance for personal and professional growth and provide necessary instructions regarding final paycheck and return of company property.",
                'module'=>'termination',
                'field_json'=>'{"field":[{"label":"Termination reasons","placeholder":"e.g.Poor Performance","field_type":"textarea","field_name":"reasons"}]}',
                'is_tone'=>'1',
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
            [
                'template_name'=>'description',
                'prompt'=>"Generate an announcement title for ##reasons##. The title should be attention-grabbing and informative, effectively conveying the key message to the intended audience. Please ensure the title is appropriate for the given situation, whether it's about a ##reasons##. Aim to create a title that captures the essence of the announcement and sparks interest or curiosity among the readers.",
                'module'=>'announcement',
                'field_json'=>'{"field":[{"label":"Announcement reasons","placeholder":"e.g.Growth Opportunities","field_type":"textarea","field_name":"reasons"}]}',
                'is_tone'=>'1',
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
            [
                'template_name'=>'occasion',
                'prompt'=>"Generate a list of holiday occasions for celebrations and gatherings. The occasions should cover a variety of holidays and events throughout the year, such as ##name##. Please provide a diverse range of occasions that can be used for hosting parties, organizing special events, or planning festive activities. Aim to offer unique and creative ideas that cater to different cultures, traditions, and preferences.",
                'module'=>'holiday',
                'field_json'=>'{"field":[{"label":"Any Specific occasions","placeholder":"e.g.Cultural Celebration","field_type":"text_box","field_name":"name"}]}',
                'is_tone'=>'0',
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
            [
                'template_name'=>'title',
                'prompt'=>"Generate a creative and engaging event title for an upcoming event. The event can be a ##type##. Please focus on creating a title that captures the essence of the event, sparks curiosity, and encourages attendance. Aim to make the title memorable, intriguing, and aligned with the purpose and theme of the event. Consider the target audience, event objectives, and any specific keywords or ideas you would like to incorporate",
                'module'=>'event',
                'field_json'=>'{"field":[{"label":"Specific type of event","placeholder":"e.g.conference, workshop, seminar","field_type":"text_box","field_name":"name"}]}',
                'is_tone'=>'0',
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
            [
                'template_name'=>'title',
                'prompt'=>"Generate a meeting title that is catchy and informative. The title should effectively convey the purpose and focus of the meeting, whether it's for ##purpose##. Please aim to create a title that grabs the attention of participants, reflects the importance of the meeting, and provides a clear understanding of what will be discussed or accomplished during the session.",
                'module'=>'meeting',
                'field_json'=>'{"field":[{"label":"Meeting purpose","placeholder":"e.g.conference, workshop","field_type":"textarea","field_name":"purpose"}]}',
                'is_tone'=>'0',
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
            [
                'template_name'=>'description',
                'prompt'=>"Generate a descriptive response for a given ##title##. The response should be detailed, engaging, and informative, providing relevant information and capturing the reader's interest",
                'module'=>'account asset',
                'field_json'=>'{"field":[{"label":"Asset name","placeholder":"HR may provide some devices ","field_type":"text_box","field_name":"title"}]}',
                'is_tone'=>'1',
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
            [
                'template_name'=>'description',
                'prompt'=> "Generate a description based on a given document name:##name##. The document name: ##name## represents a specific file or document, and you need a descriptive summary or overview of its contents. Please provide a clear and concise description that captures the main points, purpose, or key information contained within the document. Aim to create a brief but informative description that gives the reader an understanding of what they can expect when accessing or reviewing the document.",
                'module'=>'document',
                'field_json'=>'{"field":[{"label":"Asset name","placeholder":"e.g. Employee handbook","field_type":"text_box","field_name":"title"}]}',
                'is_tone'=>'1',
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
            [
                'template_name'=>'title',
                'prompt'=>"Generate a suitable title for the company policy regarding ##description##. The title should be clear, concise, and informative, effectively conveying the purpose and scope of the policy. Please ensure that the title reflects the importance of ##description##. Aim to create a title that is professional, easily understandable, and aligned with the company's culture and values.",
                'module'=>'company policy',
                'field_json'=>'{"field":[{"label":"Description of policy","placeholder":"e.g.Leave policies,Performance management","field_type":"textarea","field_name":"description"}]}',
                'is_tone'=>'0',
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
            [
                'template_name'=>'description',
                'prompt'=>"generate description for this title ##title##",
                'module'=>'chart of account',
                'field_json'=>'{"field":[{"label":" Title ","placeholder":"e.g.Accounts Receivable,Office Equipment","field_type":"textarea","field_name":"title"}]}',
                'is_tone'=>'1',
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
            [
                'template_name'=>'description',
                'prompt'=>"generate description for this title ##title##",
                'module'=>'journal entry',
                'field_json'=>'{"field":[{"label":" Title ","placeholder":"e.g.Accounts Receivable,Office Equipment","field_type":"textarea","field_name":"title"}]}',
                'is_tone'=>'1',
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
            [
                'template_name'=>'subject',
                'prompt'=>"Generate a lead subject line for a marketing campaign targeting potential customers for a software development company specializing in web and mobile applications.",
                'module'=>'lead',
                'field_json'=>'{"field":[{"label":"Description","placeholder":"e.g. Leads represent potential sales opportunities for a business","field_type":"textarea","field_name":"description"}]}',
                'is_tone'=>'0',
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
            [
                'template_name'=>'name',
                'prompt'=>"generate deal name for this proposal description ##description##" ,
                'module'=>'deal',
                'field_json'=>'{"field":[{"label":"Proposal Description","placeholder":"e.g.Collaboration and Partnerships","field_type":"textarea","field_name":"description"}]}',
                'is_tone'=>'0',
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
            [
                'template_name'=>'subject',
                'prompt'=>"generate contract subject for this contract description ##description##",
                'module'=>'contract',
                'field_json'=>'{"field":[{"label":"Proposal Description","placeholder":"e.g.Terms and Conditions","field_type":"textarea","field_name":"description"}]}',
                'is_tone'=>'0',
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
            [
                'template_name'=>'description',
                'prompt'=>"generate contract description for this contract subject ##subject##",
                'module'=>'contract',
                'field_json'=>'{"field":[{"label":"Contract Subject","placeholder":"e.g.Legal Protection,Terms and Conditions","field_type":"textarea","field_name":"subject"}]}',
                'is_tone'=>'1',
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
            [
                'template_name'=>'project_name',
                'prompt'=>"Create creative product names:  ##description## \n\nSeed words: ##keywords## \n\n" ,
                'module'=>'project',
                'field_json'=>'{"field":[{"label":"Project Description","placeholder":"e.g.Efficiency and Optimization,Business Growth and Expansion","field_type":"textarea","field_name":"description"}]}',
                'is_tone'=>'0',
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
            [
                'template_name'=>'name',
                'prompt'=>"Generate a task name for a project in an ##project_name##, specifically related to ##instruction##.",
                'module'=>'project task',
                'field_json'=>'{"field":[{"label":"Project name","placeholder":"e.g.Solving Problems","field_type":"text_box","field_name":"project_name"},{"label":"Task Instruction","placeholder":"e.g.Data Analysis","field_type":"textarea","field_name":"instruction"}]}',
                'is_tone'=>'0',
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
            [
                'template_name'=>'title',
                'prompt'=>"Generate a milestone name for a ##project_name##,specifically related to ##instruction##.",
                'module'=>'project milestone',
                'field_json'=>'{"field":[{"label":"Milestone Description","placeholder":"e.g.Design Approved","field_type":"textarea","field_name":"description"},{"label":" Instruction","placeholder":"e.g. incorporated feedback and revisions","field_type":"textarea","field_name":"instruction"}]}',
                'is_tone'=>'0',
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
            [
                'template_name'=>'title',
                'prompt'=>"You are a software developer working on a platform or service, and you're experiencing a bug where ##description##. You need to come up with a descriptive bug title for this issue. Please generate a few bug titles that could be used to report this problem.",
                'module'=>'project bug',
                'field_json'=>'{"field":[{"label":"Description of Bug","placeholder":"e.g.identify bugs and issues","field_type":"textarea","field_name":"description"}]}',
                'is_tone'=>'0',
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
            [
                'template_name'=>'description',
                'prompt'=>"Write a long creative product description for: ##title## \n\nTarget audience is: ##audience## \n\nUse this description: ##description## \n\nTone of generated text must be:\n ##tone_language## \n\n",
                'module'=>'productservice',
                'field_json'=>'{"field":[{"label":"Product name","placeholder":"e.g. VR, Honda","field_type":"text_box","field_name":"title"},{"label":"Audience","placeholder":"e.g. Women, Aliens","field_type":"text_box","field_name":"audience"},{"label":"Product Description","placeholder":"e.g. VR is an innovative device that can allow you to be part of virtual world","field_type":"textarea","field_name":"description"}]}',
                'is_tone'=>'1',
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
            [
                'template_name'=>'name',
                'prompt'=>"generate warehouse name for ##description##",
                'module'=>'warehouse',
                'field_json'=>'{"field":[{"label":"Description","placeholder":"e.g.North Warehouse","field_type":"textarea","field_name":"description"}]}',
                'is_tone'=>'0',
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
            [
                'template_name'=>'subject',
                'prompt'=>"generate example of  subject for bug in ecommerce base website support ticket",
                'module'=>'support',
                'field_json'=>'{"field":[{"label":"Ticket Description of Bug","placeholder":"e.g.Bug Summary","field_type":"textarea","field_name":"description"}]}',
                'is_tone'=>'0',
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
            [
                'template_name'=>'description',
                'prompt'=>"generate support ticket description of  subject for ##subject## ",
                'module'=>'support',
                'field_json'=>'{"field":[{"label":"Ticket Subject","placeholder":"e.g.Error Message Displayed","field_type":"textarea","field_name":"subject"}]}',
                'is_tone'=>'1',
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
            [
                'template_name'=>'title',
                'prompt'=>"Generate a list of Zoom meeting topics for ##description## metting. The purpose of the meeting is to  ##description##. Structure the topics to ensure a productive discussion.",
                'module'=>'zoom meeting',
                'field_json'=>'{"field":[{"label":"Meeting description ","placeholder":"e.g.Remote Collaboration","field_type":"textarea","field_name":"description"}]}',
                'is_tone'=>'0',
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
            [
                'template_name'=>'content',
                'prompt'=>"Generate a meeting notification message for an ##topic## meeting. Include the date, time, location, and a brief agenda with three key discussion points.",
                'module'=>'notification template',
                'field_json'=>'{"field":[{"label":"Notification Message","placeholder":"e.g.brief explanation of the purpose or background of the notification","field_type":"textarea","field_name":"topic"}]}',
                'is_tone'=>'0',
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
//            [
//                'template_name'=>'content',
//                'prompt'=>"Generate a joining offer letter for {applicant_name} who has been selected for the position of {job_title} at {app_name}. Customize the letter by filling in the appropriate details and placeholders enclosed in {}. Please structure the letter to include the mentioned variables in their respective sections. Sign off the letter with the company name.Variables:{applicant_name},{app_name},{job_title},{start_date},{workplace_location},{days_of_week},{salary},{salary_type},{offer_expiration_date} and  letter must be descriptive with eficient information",
//                'module'=>'offer letter',
//                'field_json'=>'',
//                'is_tone'=>'1',
//                "created_at" => date('Y-m-d H:i:s'),
//                "updated_at" => date('Y-m-d H:i:s'),
//            ],
//            [
//                'template_name'=>'content',
//                'prompt'=>"Generate a joining letter for {employee_name} who has been selected for the position of {designation} at {app_name}. Customize the letter by filling in the appropriate details and placeholders enclosed in {}. Please structure the letter to include the mentioned Variables:{date},{employee_name}{address},{designation},{start_date},{branch},{start_time},{end_time}, {total_hours} in their respective sections. Sign off the letter with the company name and the date. alse add subject ,company conditions for ##conditions##",
//                'module'=>'joining letter',
//                'field_json'=>'{"field":[{"label":"Company  Policy/Condition (comma seperated string)","placeholder":"e.g.,"leave,holiday,salary":"textarea","field_name":"conditions"}]}',
//                'is_tone'=>'1',
//                "created_at" => date('Y-m-d H:i:s'),
//                "updated_at" => date('Y-m-d H:i:s'),
//            ],
            [
                'template_name'=>'name',
                'prompt'=>"please suggest subscription plan  name  for this  :  ##description##  for my business",
                'module'=>'plan',
                'field_json'=>'{"field":[{"label":"What is your plan about?","placeholder":"e.g. Describe your plan details ","field_type":"textarea","field_name":"description"}]}',
                'is_tone'=>'0',
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
            [
                'template_name'=>'description',
                'prompt'=>"please suggest subscription plan  description  for this  :  ##title##:  for my business",
                'module'=>'plan',
                'field_json'=>'{"field":[{"label":"What is your plan title?","placeholder":"e.g. Pro Resller,Exclusive Access","field_type":"text_box","field_name":"title"}]}',
                'is_tone'=>'1',
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
            [
                'template_name'=>'name',
                'prompt'=>"give 10 catchy only name of Offer or discount Coupon for : ##keywords##",
                'module'=>'coupon',
                'field_json'=>'{"field":[{"label":"Seed words","placeholder":"e.g.coupon will provide you with a discount on your selected plan","field_type":"text_box","field_name":"keywords"}]}',
                'is_tone'=>'0',
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
            [
                'template_name'=>'meta_title',
                'prompt'=>"Write SEO meta title for:\n\n ##description## \n\nWebsite name is:\n ##title## \n\nSeed words:\n ##keywords## \n\n",
                'module'=>'seo',
                'field_json'=>'{"field":[{"label":"Website Name","placeholder":"e.g. Amazon, Google","field_type":"text_box","field_name":"title"},{"label":"Website Description","placeholder":"e.g. Describe what your website or business do","field_type":"textarea","field_name":"description"},{"label":"Keywords","placeholder":"e.g.  cloud services, databases","field_type":"text_box","field_name":"keywords"}]}',
                'is_tone'=>'0',
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
            [
                'template_name'=>'meta_desc',
                'prompt'=>"Write SEO meta description for:\n\n ##description## \n\nWebsite name is:\n ##title## \n\nSeed words:\n ##keywords## \n\n",
                'module'=>'seo',
                'field_json'=>'{"field":[{"label":"Website Name","placeholder":"e.g. Amazon, Google","field_type":"text_box","field_name":"title"},{"label":"Website Description","placeholder":"e.g. Describe what your website or business do","field_type":"textarea","field_name":"description"},{"label":"Keywords","placeholder":"e.g.  cloud services, databases","field_type":"text_box","field_name":"keywords"}]}',
                'is_tone'=>'1',
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],[
                'template_name'=>'cookie_title',
                'prompt'=>"please suggest me cookie title for this ##description## website which i can use in my website cookie",
                'module'=>'cookie',
                'field_json'=>'{"field":[{"label":"Website name or info","placeholder":"e.g. example website ","field_type":"textarea","field_name":"title"}]}',
                'is_tone'=>'0',
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],[
                'template_name'=>'cookie_description',
                'prompt'=>"please suggest me  Cookie description for this cookie title ##title##  which i can use in my website cookie",
                'module'=>'cookie',
                'field_json'=>'{"field":[{"label":"Cookie Title ","placeholder":"e.g. example website ","field_type":"text_box","field_name":"title"}]}',
                'is_tone'=>'1',
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
            [
                'template_name'=>'strictly_cookie_title',
                'prompt'=>"please suggest me only Strictly Cookie Title for this ##description## website which i can use in my website cookie",
                'module'=>'cookie',
                'field_json'=>'{"field":[{"label":"Website name or info","placeholder":"e.g. example website ","field_type":"textarea","field_name":"title"}]}',
                'is_tone'=>'0',
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
            [
                'template_name'=>'strictly_cookie_description',
                'prompt'=>"please suggest me Strictly Cookie description for this Strictly cookie title ##title##  which i can use in my website cookie",
                'module'=>'cookie',
                'field_json'=>'{"field":[{"label":"Strictly Cookie Title ","placeholder":"e.g. example website ","field_type":"text_box","field_name":"title"}]}',
                'is_tone'=>'1',
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
            [
                'template_name'=>'more_information_description',
                'prompt'=>"I need assistance in crafting compelling content for my ##web_name## website's 'Contact Us' page of my website. The page should provide relevant information to users, encourage them to reach out for inquiries, support, and feedback, and reflect the unique value proposition of my business.",
                'module'=>'cookie',
                'field_json'=>'{"field":[{"label":"Websit Name","placeholder":"e.g. example website ","field_type":"text_box","field_name":"web_name"}]}',
                'is_tone'=>'1',
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
            [
                'template_name'=>'content',
                'prompt'=>"generate email template for ##type##",
                'module'=>'email template',
                'field_json'=>'{"field":[{"label":"Email Type","placeholder":"e.g. new user,new client","field_type":"text_box","field_name":"type"}]}',
                'is_tone'=>'1',
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
            [
                'template_name'=>'note',
                'prompt'=>"Generate short description Note for lead ##description##",
                'module'=>'lead',
                'field_json'=>'{"field":[{"label":"Lead description","placeholder":"e.g. create notes for lead user ","field_type":"textarea","field_name":"description"}]}',
                'is_tone'=>'1',
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],

            [
                'template_name'=>'description',
                'prompt'=>"Generate a short note summarizing the key points discussed during a lead ##name## call. The purpose of the note is to capture important details and action items discussed with the ##name## lead. Please structure the note in a concise and organized manner.",
                'module'=>'lead',
                'field_json'=>'{"field":[{"label":"Lead name","placeholder":"e.g. create description for lead user ","field_type":"textarea","field_name":"name"}]}',
                'is_tone'=>'1',
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
            [
                'template_name'=>'note',
                'prompt'=>"Generate short description Note for deal ##description##",
                'module'=>'deal',
                'field_json'=>'{"field":[{"label":"Deal description","placeholder":"e.g.create note for deal client","field_type":"textarea","field_name":"description"}]}',
                'is_tone'=>'1',
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],

            [
                'template_name'=>'description',
                'prompt'=>"Generate a short note summarizing a deal call. Imagine you just had a call with a potential client or partner to discuss a ##description## deal. Write a concise summary of the key points discussed during the call. Include the important details such as the client's name, the purpose of the call, any agreements or decisions made, and next steps to be taken.",
                'module'=>'deal',
                'field_json'=>'{"field":[{"label":"Deal name","placeholder":"e.g. Establishing Communication ","field_type":"textarea","field_name":"description"}]}',
                'is_tone'=>'1',
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],



        ];
        Template::insert($template);
    }
}
