<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use App\Models\User;
use App\Models\warehouse;

class AdvancedCashManagementTestDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get first user and warehouse for testing
        $user = User::first();
        $warehouse = warehouse::first();
        
        if (!$user || !$warehouse) {
            $this->command->error('No users or warehouses found. Please create them first.');
            return;
        }

        $this->command->info('Creating test data for Advanced Cash Management...');

        // Create test shifts
        $this->createTestShifts($user, $warehouse);
        
        // Create test receipt vouchers
        $this->createTestReceiptVouchers($user, $warehouse);
        
        // Create test payment vouchers
        $this->createTestPaymentVouchers($user, $warehouse);
        
        // Create test POS data
        $this->createTestPOSData($user, $warehouse);

        $this->command->info('Test data created successfully!');
    }

    private function createTestShifts($user, $warehouse)
    {
        $this->command->info('Creating test shifts...');

        // Create an open shift
        $openShift = DB::table('shifts')->insertGetId([
            'shift_opening_balance' => 5000.00,
            'is_closed' => false,
            'opened_at' => Carbon::now()->subHours(8),
            'closed_at' => null,
            'warehouse_id' => $warehouse->id,
            'created_by' => $user->id,
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ]);

        // Create financial record for open shift
        DB::table('financial_records')->insert([
            'opening_balance' => 5000.00,
            'current_cash' => 7500.00,
            'overnetwork_cash' => 2000.00,
            'delivery_cash' => 500.00,
            'total_cash' => 9500.00,
            'deficit' => 250.00, // Add some deficit for testing
            'received_advance' => 0.00,
            'shift_id' => $openShift,
            'created_by' => $user->id,
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ]);

        // Create a closed shift
        $closedShift = DB::table('shifts')->insertGetId([
            'shift_opening_balance' => 3000.00,
            'is_closed' => true,
            'opened_at' => Carbon::yesterday()->setHour(9),
            'closed_at' => Carbon::yesterday()->setHour(17),
            'warehouse_id' => $warehouse->id,
            'created_by' => $user->id,
            'closed_by' => $user->id,
            'created_at' => Carbon::yesterday(),
            'updated_at' => Carbon::yesterday(),
        ]);

        // Create financial record for closed shift
        DB::table('financial_records')->insert([
            'opening_balance' => 3000.00,
            'current_cash' => 4500.00,
            'overnetwork_cash' => 1500.00,
            'delivery_cash' => 200.00,
            'total_cash' => 6000.00,
            'deficit' => 0.00, // No deficit for closed shift
            'received_advance' => 0.00,
            'shift_id' => $closedShift,
            'created_by' => $user->id,
            'created_at' => Carbon::yesterday(),
            'updated_at' => Carbon::yesterday(),
        ]);

        // Create another shift with high deficit for testing
        $deficitShift = DB::table('shifts')->insertGetId([
            'shift_opening_balance' => 4000.00,
            'is_closed' => false,
            'opened_at' => Carbon::now()->subHours(15), // Long open shift
            'closed_at' => null,
            'warehouse_id' => $warehouse->id,
            'created_by' => $user->id,
            'created_at' => Carbon::now()->subHours(15),
            'updated_at' => Carbon::now(),
        ]);

        // Create financial record with high deficit
        DB::table('financial_records')->insert([
            'opening_balance' => 4000.00,
            'current_cash' => 3200.00,
            'overnetwork_cash' => 1800.00,
            'delivery_cash' => 300.00,
            'total_cash' => 5000.00,
            'deficit' => 800.00, // High deficit for alert testing
            'received_advance' => 0.00,
            'shift_id' => $deficitShift,
            'created_by' => $user->id,
            'created_at' => Carbon::now()->subHours(15),
            'updated_at' => Carbon::now(),
        ]);
    }

    private function createTestReceiptVouchers($user, $warehouse)
    {
        $this->command->info('Creating test receipt vouchers...');

        $receiptVouchers = [
            [
                'custome_id' => 'RC-001-' . date('Y'),
                'payment_amount' => 2500.00,
                'payment_method' => 'cash',
                'purpose' => 'دفعة من العميل أحمد محمد',
                'date' => Carbon::today(),
                'receipt_from_user_id' => $user->id,
                'warehouse_id' => $warehouse->id,
                'created_by' => $user->id,
                'status' => 'accepted',
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'custome_id' => 'RC-002-' . date('Y'),
                'payment_amount' => 1800.00,
                'payment_method' => 'bank_transfer',
                'purpose' => 'تحويل بنكي من شركة الخليج',
                'date' => Carbon::today(),
                'receipt_from_user_id' => $user->id,
                'warehouse_id' => $warehouse->id,
                'created_by' => $user->id,
                'status' => 'accepted',
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'custome_id' => 'RC-003-' . date('Y'),
                'payment_amount' => 3200.00,
                'payment_method' => 'cash',
                'purpose' => 'دفعة نقدية من مؤسسة النور',
                'date' => Carbon::yesterday(),
                'receipt_from_user_id' => $user->id,
                'warehouse_id' => $warehouse->id,
                'created_by' => $user->id,
                'status' => 'accepted',
                'created_at' => Carbon::yesterday(),
                'updated_at' => Carbon::yesterday(),
            ],
        ];

        DB::table('voucher_receipts')->insert($receiptVouchers);
    }

    private function createTestPaymentVouchers($user, $warehouse)
    {
        $this->command->info('Creating test payment vouchers...');

        $paymentVouchers = [
            [
                'custome_id' => 'PY-001-' . date('Y'),
                'payment_amount' => 1200.00,
                'payment_method' => 'cash',
                'purpose' => 'دفع فاتورة كهرباء',
                'date' => Carbon::today(),
                'pay_to_user_id' => $user->id,
                'warehouse_id' => $warehouse->id,
                'created_by' => $user->id,
                'status' => 'accepted',
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'custome_id' => 'PY-002-' . date('Y'),
                'payment_amount' => 800.00,
                'payment_method' => 'bank_transfer',
                'purpose' => 'راتب موظف',
                'date' => Carbon::today(),
                'pay_to_user_id' => $user->id,
                'warehouse_id' => $warehouse->id,
                'created_by' => $user->id,
                'status' => 'accepted',
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'custome_id' => 'PY-003-' . date('Y'),
                'payment_amount' => 1500.00,
                'payment_method' => 'cash',
                'purpose' => 'دفع لمورد',
                'date' => Carbon::yesterday(),
                'pay_to_user_id' => $user->id,
                'warehouse_id' => $warehouse->id,
                'created_by' => $user->id,
                'status' => 'accepted',
                'created_at' => Carbon::yesterday(),
                'updated_at' => Carbon::yesterday(),
            ],
        ];

        DB::table('voucher_payments')->insert($paymentVouchers);
    }

    private function createTestPOSData($user, $warehouse)
    {
        $this->command->info('Creating test POS data...');

        // Create test POS transactions
        $posTransactions = [
            [
                'pos_id' => 'POS-001-' . date('Y'),
                'customer_id' => 1, // Assuming customer exists
                'warehouse_id' => $warehouse->id,
                'pos_date' => Carbon::today(),
                'created_by' => $user->id,
                'is_payment_set' => 1,
                'user_id' => $user->id,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'pos_id' => 'POS-002-' . date('Y'),
                'customer_id' => 1,
                'warehouse_id' => $warehouse->id,
                'pos_date' => Carbon::today(),
                'created_by' => $user->id,
                'is_payment_set' => 1,
                'user_id' => $user->id,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'pos_id' => 'POS-003-' . date('Y'),
                'customer_id' => 1,
                'warehouse_id' => $warehouse->id,
                'pos_date' => Carbon::yesterday(),
                'created_by' => $user->id,
                'is_payment_set' => 1,
                'user_id' => $user->id,
                'created_at' => Carbon::yesterday(),
                'updated_at' => Carbon::yesterday(),
            ],
        ];

        foreach ($posTransactions as $index => $pos) {
            $posId = DB::table('pos')->insertGetId($pos);

            // Create corresponding payment
            DB::table('pos_payments')->insert([
                'pos_id' => $posId,
                'date' => $pos['pos_date'],
                'amount' => ($index + 1) * 150.00, // Different amounts
                'discount' => 0.00,
                'discount_amount' => 0.00,
                'payment_type' => $index % 2 == 0 ? 'cash' : 'network',
                'cash_amount' => $index % 2 == 0 ? ($index + 1) * 150.00 : 0.00,
                'network_amount' => $index % 2 == 1 ? ($index + 1) * 150.00 : 0.00,
                'transaction_number' => $index % 2 == 1 ? 'TXN-' . (1000 + $index) : null,
                'created_by' => $user->id,
                'created_at' => $pos['created_at'],
                'updated_at' => $pos['updated_at'],
            ]);
        }
    }
}
