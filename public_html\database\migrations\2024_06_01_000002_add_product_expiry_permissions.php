<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add new permissions for product expiry
        $permissions = [
            [
                'name' => 'show product expiry',
                'guard_name' => 'web',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'edit product expiry',
                'guard_name' => 'web',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
        ];

        // Insert permissions
        foreach ($permissions as $permission) {
            if (!Permission::where('name', $permission['name'])->exists()) {
                Permission::create($permission);
            }
        }

        // Assign permissions to Cashier role
        $cashierRole = Role::where('name', 'Cashier')->first();
        if ($cashierRole) {
            $cashierRole->givePermissionTo('show product expiry');
            $cashierRole->givePermissionTo('edit product expiry');
        }

        // Assign permissions to SUPER FIESR role
        $superFiesrRole = Role::where('name', 'SUPER FIESR')->first();
        if ($superFiesrRole) {
            $superFiesrRole->givePermissionTo('show product expiry');
            $superFiesrRole->givePermissionTo('edit product expiry');
        }

        // Assign permissions to company role
        $companyRole = Role::where('name', 'company')->first();
        if ($companyRole) {
            $companyRole->givePermissionTo('show product expiry');
            $companyRole->givePermissionTo('edit product expiry');
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove permissions
        $permissions = [
            'show product expiry',
            'edit product expiry',
        ];

        foreach ($permissions as $permission) {
            $p = Permission::where('name', $permission)->first();
            if ($p) {
                $p->delete();
            }
        }
    }
};
