<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;

class PosV2 extends Model
{
    protected $table = 'pos_v2';
    
    protected $fillable = [
        'pos_id',
        'customer_id',
        'warehouse_id',
        'pos_date',
        'category_id',
        'status',
        'status_type',
        'shipping_display',
        'created_by',
        'is_payment_set',
        'user_id',
        'shift_id',
    ];

    /**
     * القيم المحتملة لحقل status_type
     */
    public static $statusTypes = [
        'normal' => 'عادي',
        'returned' => 'مرتجع بضاعة',
        'cancelled' => 'ملغية',
    ];

    public function customer()
    {
        return $this->hasOne('App\Models\Customer', 'id', 'customer_id');
    }
    
    public function warehouse()
    {
        return $this->hasOne('App\Models\warehouse', 'id', 'warehouse_id');
    }

    public function posPayment()
    {
        return $this->hasOne('App\Models\PosV2Payment','pos_id','id');
    }

    public function payments()
    {
        return $this->hasMany('App\Models\PosV2Payment','pos_id','id');
    }

    public function items()
    {
        return $this->hasMany('App\Models\PosV2Product', 'pos_id', 'id');
    }

    public function createdBy()
    {
        return $this->hasOne('App\Models\User', 'id', 'created_by');
    }

    public function user()
    {
        return $this->hasOne('App\Models\User', 'id', 'user_id');
    }

    public function shift()
    {
        return $this->hasOne('App\Models\Shift', 'id', 'shift_id');
    }

    public function getSubTotal()
    {
        $subTotal = 0;
        foreach ($this->items as $product) {
            $subTotal += ($product->price * $product->quantity);
        }

        return $subTotal;
    }

    public function getTotalTax()
    {
        $totalTax = 0;
        foreach ($this->items as $product) {
            $taxes = \Utility::totalTaxRate($product->tax);

            $totalTax += ($taxes / 100) * ($product->price * $product->quantity);
        }

        return $totalTax;
    }

    public function getTotalDiscount()
    {
        $totalDiscount = 0;
        foreach ($this->items as $product) {
            $totalDiscount += $product->discount;
        }

        return $totalDiscount;
    }

    public function getTotal()
    {
        return ($this->getSubTotal() - $this->getTotalDiscount()) + $this->getTotalTax();
    }

    public static function getPosV2ProductsData($month = '')
    {
        if (!empty($month)) {
            $posProducts = PosV2Product::select('*')
                ->join('pos_v2', 'pos_v2_products.pos_id', '=', 'pos_v2.id')
                ->whereMonth('pos_v2.created_at', $month)
                ->where('pos_v2.created_by', \Auth::user()->creatorId())
                ->groupBy('pos_v2.id')
                ->get()
                ->keyBy('pos_id');
        } else {
            $posProducts = PosV2Product::select('*')
                ->join('pos_v2', 'pos_v2_products.pos_id', '=', 'pos_v2.id')
                ->where('pos_v2.created_by', \Auth::user()->creatorId())
                ->groupBy('pos_v2.id')
                ->get()
                ->keyBy('pos_id');
        }
        
        $total = 0;

        foreach($posProducts as $pos) {
            $getTaxData = Utility::getTaxData();
            $totalTaxPrice = 0;
            if (!empty($pos->tax)) {
                foreach (explode(',', $pos->tax ?? '') as $tax) {
                    $taxPrice = \Utility::taxRate($getTaxData[$tax]['rate'], $pos->price, $pos->quantity , $pos->total_discount);
                    $totalTaxPrice += $taxPrice;
                }
            }

            $total += ($pos->price  * $pos->quantity) + $totalTaxPrice - $pos->total_discount;
        }
        
        return $total;
    }

    public static function totalPosV2Amount($month = false)
    {
        $posAmount = self::getPosV2ProductsData($month);

        return Auth::user()->priceFormat($posAmount);
    }
}
