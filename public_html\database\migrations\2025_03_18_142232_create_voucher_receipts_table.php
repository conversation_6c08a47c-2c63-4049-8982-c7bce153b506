<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('voucher_receipts', function (Blueprint $table) {
            $table->id();
            $table->string('custome_id')->nullable();
            $table->decimal('payment_amount', 15, 2)->nullable()->default(0);
            $table->string('payment_method')->nullable();
            $table->string('purpose')->nullable();
            $table->enum('status', ['pending', 'rejected', 'accepted'])
                ->default('pending');
            $table->date('date')->nullable();
            $table->foreignId('receipt_from_user_id')->constrained('users')->cascadeOnDelete();
            $table->foreignId('created_by')->constrained('users')->cascadeOnDelete();
            $table->foreignId('updated_by')->nullable()->constrained('users')->nullOnDelete();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('voucher_receipts');
    }
};
