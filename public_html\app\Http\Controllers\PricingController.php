<?php

namespace App\Http\Controllers;

use App\Models\ProductService;
use App\Models\ProductServiceCategory;
use App\Models\ProductServiceUnit;
use App\Models\ChartOfAccount;
use App\Models\Tax;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class PricingController extends Controller
{
    /**
     * عرض صفحة التسعير مع جميع المنتجات
     */
    public function index(Request $request)
    {
        // التحقق من الصلاحيات - متاح لمستخدمي الشركة والمحاسبة والتسعير
        if (!Auth::user()->hasRole('company') &&
            !Auth::user()->hasRole('accountant') &&
            !Auth::user()->hasRole('Pricing') &&
            !Auth::user()->can('manage pricing') &&
            !Auth::user()->can('manage product & service')) {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        // جلب جميع المنتجات مع العلاقات
        $query = ProductService::where('created_by', '=', Auth::user()->creatorId())
            ->with(['category', 'unit']);

        // تطبيق فلاتر البحث
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('sku', 'like', "%{$search}%");
            });
        }

        if ($request->filled('category_id')) {
            $query->where('category_id', $request->category_id);
        }

        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        $products = $query->orderBy('id', 'desc')->get();

        // اكتشاف SKU المكررة
        $skuCounts = $products->groupBy('sku')->map(function ($group) {
            return $group->count();
        });

        $duplicateSkus = $skuCounts->filter(function ($count) {
            return $count > 1;
        })->keys()->toArray();

        // تطبيق فلتر المكررات إذا تم طلبه
        if ($request->filled('show_duplicates') && $request->show_duplicates == '1') {
            $products = $products->filter(function ($product) use ($duplicateSkus) {
                return in_array($product->sku, $duplicateSkus);
            });
        }

        // جلب البيانات المساعدة للفلاتر
        $categories = ProductServiceCategory::where('created_by', '=', Auth::user()->creatorId())
            ->where('type', '=', 'product & service')
            ->get()
            ->pluck('name', 'id');

        $units = ProductServiceUnit::where('created_by', '=', Auth::user()->creatorId())
            ->get()
            ->pluck('name', 'id');

        $expenseAccounts = ChartOfAccount::where('created_by', '=', Auth::user()->creatorId())
            ->get()
            ->pluck('name', 'id');

        $types = [
            'Product' => __('Product'),
            'Service' => __('Service')
        ];

        return view('pricing.index', compact(
            'products',
            'categories',
            'units',
            'expenseAccounts',
            'types',
            'duplicateSkus'
        ));
    }

    /**
     * عرض صفحة التسعير المبسطة للاختبار
     */
    public function simple(Request $request)
    {
        // التحقق من الصلاحيات - متاح لمستخدمي الشركة والمحاسبة والتسعير
        if (!Auth::user()->hasRole('company') &&
            !Auth::user()->hasRole('accountant') &&
            !Auth::user()->hasRole('Pricing') &&
            !Auth::user()->can('manage pricing') &&
            !Auth::user()->can('manage product & service')) {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        // جلب أول 10 منتجات للاختبار
        $products = ProductService::where('created_by', '=', Auth::user()->creatorId())
            ->limit(10)
            ->get();

        return view('pricing.simple', compact('products'));
    }

    /**
     * تحديث البيانات مباشرة (Inline Editing)
     */
    public function updateInline(Request $request)
    {
        try {
            // التحقق من الصلاحيات - متاح لمستخدمي الشركة والمحاسبة والتسعير
            if (!Auth::user()->hasRole('company') &&
                !Auth::user()->hasRole('accountant') &&
                !Auth::user()->hasRole('Pricing') &&
                !Auth::user()->can('edit pricing') &&
                !Auth::user()->can('edit product & service')) {
                return response()->json([
                    'success' => false,
                    'message' => __('Permission denied.')
                ], 403);
            }

            // التحقق من صحة البيانات
            $validator = Validator::make($request->all(), [
                'id' => 'required|integer|exists:product_services,id',
                'field' => 'required|string|in:name,sku,sale_price,purchase_price,quantity,expense_chartaccount_id,sale_chartaccount_id,category_id,unit_id,type',
                'value' => 'required'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => $validator->errors()->first()
                ], 400);
            }

            // جلب المنتج
            $product = ProductService::where('id', $request->id)
                ->where('created_by', Auth::user()->creatorId())
                ->first();

            if (!$product) {
                return response()->json([
                    'success' => false,
                    'message' => __('Product not found.')
                ], 404);
            }

            // تحديد قواعد التحقق حسب الحقل
            $fieldRules = [
                'name' => 'required|string|max:255',
                'sku' => 'required|string|max:255|unique:product_services,sku,' . $product->id,
                'sale_price' => 'required|numeric|min:0',
                'purchase_price' => 'required|numeric|min:0',
                'quantity' => 'required|numeric|min:0',
                'expense_chartaccount_id' => 'required|integer|exists:chart_of_accounts,id',
                'sale_chartaccount_id' => 'required|integer|exists:chart_of_accounts,id',
                'category_id' => 'required|integer|exists:product_service_categories,id',
                'unit_id' => 'required|integer|exists:product_service_units,id',
                'type' => 'required|string|in:Product,Service'
            ];

            // التحقق من صحة القيمة الجديدة
            $fieldValidator = Validator::make(
                ['value' => $request->value],
                ['value' => $fieldRules[$request->field]]
            );

            if ($fieldValidator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => $fieldValidator->errors()->first()
                ], 400);
            }

            // تحديث الحقل
            $product->{$request->field} = $request->value;
            $product->save();

            // إرجاع القيمة المحدثة مع التنسيق المناسب
            $displayValue = $this->formatDisplayValue($product, $request->field);

            return response()->json([
                'success' => true,
                'message' => __('Updated successfully.'),
                'display_value' => $displayValue
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => __('An error occurred: ') . $e->getMessage()
            ], 500);
        }
    }

    /**
     * جلب خيارات الحقول للتعديل المباشر
     */
    public function getFieldOptions(Request $request)
    {
        try {
            $field = $request->get('field');
            $options = [];

            switch ($field) {
                case 'category_id':
                    $categories = ProductServiceCategory::where('created_by', '=', Auth::user()->creatorId())
                        ->where('type', '=', 'product & service')
                        ->get();
                    foreach ($categories as $category) {
                        $options[] = [
                            'value' => $category->id,
                            'text' => $category->name
                        ];
                    }
                    break;

                case 'unit_id':
                    $units = ProductServiceUnit::where('created_by', '=', Auth::user()->creatorId())
                        ->get();
                    foreach ($units as $unit) {
                        $options[] = [
                            'value' => $unit->id,
                            'text' => $unit->name
                        ];
                    }
                    break;

                case 'expense_chartaccount_id':
                case 'sale_chartaccount_id':
                    $accounts = ChartOfAccount::where('created_by', '=', Auth::user()->creatorId())
                        ->get();
                    foreach ($accounts as $account) {
                        $options[] = [
                            'value' => $account->id,
                            'text' => $account->name
                        ];
                    }
                    break;



                case 'type':
                    $options = [
                        ['value' => 'Product', 'text' => __('Product')],
                        ['value' => 'Service', 'text' => __('Service')]
                    ];
                    break;
            }

            return response()->json([
                'success' => true,
                'options' => $options
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => __('An error occurred: ') . $e->getMessage()
            ], 500);
        }
    }

    /**
     * تنسيق القيمة للعرض
     */
    private function formatDisplayValue($product, $field)
    {
        switch ($field) {
            case 'sale_price':
            case 'purchase_price':
                return Auth::user()->priceFormat($product->{$field});

            case 'quantity':
                return number_format($product->quantity, 2);
            
            case 'category_id':
                return $product->category ? $product->category->name : '-';
            
            case 'unit_id':
                return $product->unit ? $product->unit->name : '-';
            
            case 'expense_chartaccount_id':
                $account = ChartOfAccount::find($product->expense_chartaccount_id);
                return $account ? $account->name : '-';

            case 'sale_chartaccount_id':
                $account = ChartOfAccount::find($product->sale_chartaccount_id);
                return $account ? $account->name : '-';
            
            case 'type':
                return __($product->type);
            
            default:
                return $product->{$field};
        }
    }
}
