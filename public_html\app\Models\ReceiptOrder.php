<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ReceiptOrder extends Model
{
    use HasFactory;

    protected $fillable = [
        'order_number',
        'order_type',
        'vendor_id',
        'warehouse_id',
        'from_warehouse_id',
        'invoice_number',
        'invoice_total',
        'invoice_date',
        'has_return',
        'total_products',
        'total_amount',
        'notes',
        'created_by',
        'exit_reason',
        'exit_date',
        'responsible_person',
    ];

    protected $casts = [
        'invoice_date' => 'date',
        'invoice_total' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'has_return' => 'boolean',
    ];

    /**
     * العلاقة مع المورد
     */
    public function vendor()
    {
        return $this->belongsTo(Vender::class, 'vendor_id');
    }

    /**
     * العلاقة مع المستودع الهدف
     */
    public function warehouse()
    {
        return $this->belongsTo(warehouse::class, 'warehouse_id');
    }

    /**
     * العلاقة مع المستودع المصدر (في حالة النقل)
     */
    public function fromWarehouse()
    {
        return $this->belongsTo(warehouse::class, 'from_warehouse_id');
    }

    /**
     * العلاقة مع منتجات الأمر
     */
    public function products()
    {
        return $this->hasMany(ReceiptOrderProduct::class, 'receipt_order_id');
    }



    /**
     * توليد رقم أمر تلقائي
     */
    public static function generateOrderNumber()
    {
        $lastOrder = self::orderBy('id', 'desc')->first();
        $nextNumber = $lastOrder ? $lastOrder->id + 1 : 1;
        
        return 'RO-' . date('Y') . '-' . str_pad($nextNumber, 6, '0', STR_PAD_LEFT);
    }

    /**
     * حساب إجمالي المنتجات
     */
    public function calculateTotalProducts()
    {
        return $this->products()->count();
    }

    /**
     * حساب إجمالي المبلغ
     */
    public function calculateTotalAmount()
    {
        return $this->products()->sum(DB::raw('quantity * unit_cost'));
    }

    /**
     * حساب تكلفة الوحدة المتوسطة
     */
    public function getAverageUnitCost()
    {
        $totalProducts = $this->calculateTotalProducts();
        $totalAmount = $this->calculateTotalAmount();
        
        return $totalProducts > 0 ? $totalAmount / $totalProducts : 0;
    }

    /**
     * التحقق من وجود مرتجعات
     */
    public function hasReturns()
    {
        return $this->products()->where('is_return', true)->exists();
    }

    /**
     * الحصول على المنتجات المرتجعة
     */
    public function returnedProducts()
    {
        return $this->products()->where('is_return', true);
    }

    /**
     * الحصول على المنتجات العادية (غير مرتجعة)
     */
    public function normalProducts()
    {
        return $this->products()->where('is_return', false);
    }

    /**
     * تحديث الإحصائيات
     */
    public function updateStatistics()
    {
        $this->total_products = $this->calculateTotalProducts();
        $this->total_amount = $this->calculateTotalAmount();
        $this->save();
    }

    /**
     * الحصول على نوع الأمر مع الترجمة
     */
    public function getOrderTypeAttribute($value)
    {
        return $value;
    }

    /**
     * الحصول على حالة الأمر
     */
    public function getStatusAttribute()
    {
        if ($this->order_type === 'استلام بضاعة') {
            return 'مكتمل';
        } elseif ($this->order_type === 'نقل بضاعة') {
            return 'منقول';
        } elseif ($this->order_type === 'أمر إخراج') {
            return 'مُخرج';
        }

        return 'غير محدد';
    }

    /**
     * الحصول على لون الحالة
     */
    public function getStatusColorAttribute()
    {
        if ($this->order_type === 'استلام بضاعة') {
            return 'success';
        } elseif ($this->order_type === 'نقل بضاعة') {
            return 'info';
        } elseif ($this->order_type === 'أمر إخراج') {
            return 'warning';
        }

        return 'secondary';
    }

    /**
     * البحث في الأوامر
     */
    public function scopeSearch($query, $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('order_number', 'like', "%{$search}%")
              ->orWhere('invoice_number', 'like', "%{$search}%")
              ->orWhereHas('vendor', function ($vendor) use ($search) {
                  $vendor->where('name', 'like', "%{$search}%");
              })
              ->orWhereHas('warehouse', function ($warehouse) use ($search) {
                  $warehouse->where('name', 'like', "%{$search}%");
              });
        });
    }

    /**
     * فلترة بنوع الأمر
     */
    public function scopeByType($query, $type)
    {
        return $query->where('order_type', $type);
    }

    /**
     * فلترة بالمستودع
     */
    public function scopeByWarehouse($query, $warehouseId)
    {
        return $query->where('warehouse_id', $warehouseId);
    }

    /**
     * فلترة بالمورد
     */
    public function scopeByVendor($query, $vendorId)
    {
        return $query->where('vendor_id', $vendorId);
    }

    /**
     * فلترة بالتاريخ
     */
    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('invoice_date', [$startDate, $endDate]);
    }

    /**
     * العلاقة مع المستخدم المنشئ
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }
}
