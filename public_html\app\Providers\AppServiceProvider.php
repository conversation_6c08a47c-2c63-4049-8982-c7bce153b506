<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\Facades\Request;


class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        Schema::defaultStringLength(191);

        // Force HTTPS for all URLs when using ngrok
        if (str_contains(Request::getHost(), 'ngrok-free.app')) {
            URL::forceScheme('https');

            // Set trusted proxies to trust ngrok
            if (method_exists(Request::class, 'setTrustedProxies')) {
                Request::setTrustedProxies(
                    ['127.0.0.1', Request::ip()],
                    Request::HEADER_X_FORWARDED_FOR | Request::HEADER_X_FORWARDED_HOST | Request::HEADER_X_FORWARDED_PORT | Request::HEADER_X_FORWARDED_PROTO
                );
            }
        }
    }
}
