<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class PricingRoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // إنشاء الصلاحيات الخاصة بالتسعير
        $pricingPermissions = [
            [
                "name" => "manage pricing",
                "guard_name" => "web",
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
            [
                "name" => "show pricing",
                "guard_name" => "web",
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
            [
                "name" => "edit pricing",
                "guard_name" => "web",
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
            [
                "name" => "manage inventory",
                "guard_name" => "web",
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
        ];

        // إدراج الصلاحيات إذا لم تكن موجودة
        foreach ($pricingPermissions as $permission) {
            Permission::firstOrCreate(
                ['name' => $permission['name'], 'guard_name' => $permission['guard_name']],
                $permission
            );
        }

        // البحث عن شركة موجودة لإنشاء الدور تحتها
        $company = User::where('type', 'company')->first();
        
        if (!$company) {
            $this->command->error('No company user found. Please run the main seeder first.');
            return;
        }

        // إنشاء دور "Pricing"
        $pricingRole = Role::firstOrCreate(
            ['name' => 'Pricing', 'created_by' => $company->id],
            [
                'name' => 'Pricing',
                'created_by' => $company->id,
            ]
        );

        // تحديد الصلاحيات للدور
        $pricingRolePermissions = [
            'manage pricing',
            'show pricing',
            'edit pricing',
            'manage inventory',
            'manage product & service',
            'edit product & service',
            'show product & service',
        ];

        // إعطاء الصلاحيات للدور
        $pricingRole->givePermissionTo($pricingRolePermissions);

        // إنشاء مستخدم تجريبي بدور Pricing
        $pricingUser = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Pricing Manager',
                'email' => '<EMAIL>',
                'password' => Hash::make('1234'),
                'type' => 'Pricing',
                'default_pipeline' => 1,
                'lang' => 'en',
                'avatar' => '',
                'created_by' => $company->id,
                'email_verified_at' => now(),
            ]
        );

        // تعيين الدور للمستخدم
        if (!$pricingUser->hasRole('Pricing')) {
            $pricingUser->assignRole($pricingRole);
        }

        $this->command->info('Pricing role and permissions created successfully!');
        $this->command->info('Test user created: <EMAIL> / password: 1234');
    }
}
