<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class FinancialRecord extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'opening_balance',
        'current_cash',
        'overnetwork_cash',
        'delivery_cash',
        'total_cash',
        'deficit',
        'received_advance',
        'shift_id',
        'created_by',
        'updated_by',
    ];

    /**
     * Get the creator of the record.
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the updater of the record.
     */
    public function updater()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    public function shift()
    {
        return $this->belongsTo(Shift::class, 'shift_id', 'id');
    }

}
