<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TransactionLines extends Model
{
    use HasFactory;

    // إضافة الحقول القابلة للتعبئة لتمكين التعديل المباشر
    protected $fillable = [
        'account_id',
        'reference',
        'reference_id',
        'reference_sub_id',
        'date',
        'credit',
        'debit',
        'created_by',
    ];

    // تحديد أنواع البيانات
    protected $casts = [
        'date' => 'date',
        'credit' => 'decimal:2',
        'debit' => 'decimal:2',
    ];

    // العلاقة مع جدول الحسابات
    public function chartOfAccount()
    {
        return $this->belongsTo(ChartOfAccount::class, 'account_id');
    }
}
