<?php

namespace App\Http\Controllers;

use App\Models\Message;
use App\Models\User;
use App\Models\Utility;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class MessageController extends Controller
{
    /**
     * عرض قائمة المراسلات
     */
    public function index()
    {
        if(Auth::user()->type == 'company')
        {
            $users = User::where('created_by', Auth::user()->creatorId())
                ->where('type', '!=', 'client')
                ->where('id', '!=', Auth::user()->id)
                ->get();
        }
        else
        {
            $users = User::where('created_by', Auth::user()->creatorId())
                ->where('type', '!=', 'client')
                ->where('id', '!=', Auth::user()->id)
                ->get();
        }
        
        return view('messages.index', compact('users'));
    }

    /**
     * عرض محادثة مع مستخدم معين
     */
    public function conversation($id)
    {
        $user = User::find($id);
        
        if(!$user)
        {
            return redirect()->route('messages.index')->with('error', __('User not found.'));
        }
        
        // تحديث حالة الرسائل المقروءة
        Message::where('sender_id', $id)
            ->where('receiver_id', Auth::user()->id)
            ->where('is_read', 0)
            ->update(['is_read' => 1]);
        
        // جلب الرسائل بين المستخدمين
        $messages = Message::where(function($query) use ($id) {
                $query->where('sender_id', Auth::user()->id)
                    ->where('receiver_id', $id);
            })
            ->orWhere(function($query) use ($id) {
                $query->where('sender_id', $id)
                    ->where('receiver_id', Auth::user()->id);
            })
            ->orderBy('created_at', 'asc')
            ->get();
        
        return view('messages.conversation', compact('user', 'messages'));
    }

    /**
     * إرسال رسالة جديدة
     */
    public function send(Request $request)
    {
        $request->validate([
            'receiver_id' => 'required',
            'message' => 'required',
        ]);
        
        $message = new Message();
        $message->sender_id = Auth::user()->id;
        $message->receiver_id = $request->receiver_id;
        $message->message = $request->message;
        $message->is_read = 0;
        $message->created_by = Auth::user()->creatorId();
        
        // معالجة المرفقات
        if($request->hasFile('attachment'))
        {
            $file = $request->file('attachment');
            $fileName = time() . "_" . $file->getClientOriginalName();
            $dir = 'uploads/messages';
            $path = Utility::upload_file_using_object($file, $fileName, $dir, []);
            
            if($path['flag'] == 1)
            {
                $message->attachment = $path['url'];
            }
        }
        
        $message->save();
        
        return redirect()->back()->with('success', __('Message sent successfully.'));
    }

    /**
     * الحصول على عدد الرسائل غير المقروءة
     */
    public function getUnreadCount()
    {
        $count = Message::where('receiver_id', Auth::user()->id)
            ->where('is_read', 0)
            ->count();
        
        return response()->json(['count' => $count]);
    }
}
