<?php

namespace App\Http\Requests;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;

class PaymentVoucher extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'date' => 'required|date',
            'payment_amount' => 'required|numeric',
            'pay_to_user_id' => 'required|numeric',
            'purpose' => 'required|string|max:255',
            'payment_method' => 'required|in:cash,bank_transfer',
        ];
    }

      /**
     * Get custom error messages for validation.
     */
    public function messages(): array
    {
        return [
            'date.required' => __('Date is required'),
            'payment_amount.required' => __('Payment amount is required'),
            'pay_to_user_id.required' => __('Payment to user id is required'),
            'purpose.required' => __('Purpose is required'),
            'payment_method.required' => __('Payment method is required'),
            'payment_method.in' => __('Payment method must be either cash or bank transfer'),
        ];
    }

    /**
     * Handle a failed validation attempt.
     */
    protected function failedValidation(Validator $validator)
    {
        // Redirect back with the first error message for SweetAlert
        throw new HttpResponseException(
            redirect()->back()->with('error', $validator->errors()->first())
        );
    }


}
