<?php

namespace App\Services;

use App\Models\FinancialTransactions;
use App\Models\Shift;

class FinancialTransactionService
{
    public function createFinancialTransaction($user,$transaction_type,$transaction_data)
    {
        try {
            $cash_amount = $transaction_data['cash_amount'];
            $payment_method = $transaction_data['payment_method'] == 'cash' ? 'cash' : 'bank_transfer';
            $warehouse = $user->warehouse_id;

            if(!$warehouse) {
                throw new \Exception(__('User has no warehouse'));
            }

            $openShift = Shift::whereNull('closed_at')->where('warehouse_id', $warehouse)->first();

            if (!$openShift) {
                throw new \Exception(__('No open shift found'));
            }

            $financialTransaction = FinancialTransactions::create([
                'shift_id' => $openShift->id,
                'transaction_type' => $transaction_type,
                'cash_amount' => $cash_amount,
                'created_by' => $user->id,
                'payment_method' => $payment_method,
            ]);

            if(!$financialTransaction){
                throw new \Exception(__('Failed to create financial transaction'));
            }

            return $financialTransaction;
        } catch (\Throwable $e) {
            //throw $e;
            throw $e;
        }
    }
}