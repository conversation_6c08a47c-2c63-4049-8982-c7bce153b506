<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class InvoiceAttachment extends Model
{
    use HasFactory;

    protected $fillable = [
        'invoice_id',
        'original_name',
        'file_path',
        'file_size',
        'file_type',
        'description',
        'uploaded_by',
    ];

    /**
     * Get the invoice that owns the attachment.
     */
    public function invoice()
    {
        return $this->belongsTo(Invoice::class);
    }

    /**
     * Get the user who uploaded the attachment.
     */
    public function uploader()
    {
        return $this->belongsTo(User::class, 'uploaded_by');
    }

    /**
     * Get the file size in human readable format.
     */
    public function getFileSizeHumanAttribute()
    {
        if (!$this->file_size) {
            return 'Unknown';
        }

        $bytes = $this->file_size;
        $units = ['B', 'KB', 'MB', 'GB'];
        
        for ($i = 0; $bytes > 1024; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Get the file extension.
     */
    public function getFileExtensionAttribute()
    {
        return pathinfo($this->original_name, PATHINFO_EXTENSION);
    }

    /**
     * Check if the file is an image.
     */
    public function isImage()
    {
        $imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp'];
        return in_array(strtolower($this->file_extension), $imageExtensions);
    }

    /**
     * Check if the file is a PDF.
     */
    public function isPdf()
    {
        return strtolower($this->file_extension) === 'pdf';
    }

    /**
     * Get the file icon based on file type.
     */
    public function getFileIconAttribute()
    {
        $extension = strtolower($this->file_extension);
        
        switch ($extension) {
            case 'pdf':
                return 'ti ti-file-type-pdf';
            case 'doc':
            case 'docx':
                return 'ti ti-file-type-doc';
            case 'xls':
            case 'xlsx':
                return 'ti ti-file-type-xls';
            case 'ppt':
            case 'pptx':
                return 'ti ti-file-type-ppt';
            case 'jpg':
            case 'jpeg':
            case 'png':
            case 'gif':
            case 'bmp':
            case 'svg':
            case 'webp':
                return 'ti ti-photo';
            case 'zip':
            case 'rar':
            case '7z':
                return 'ti ti-file-zip';
            case 'txt':
                return 'ti ti-file-text';
            default:
                return 'ti ti-file';
        }
    }
}
