<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Form extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'type',
        'file_path',
        'visible_to_roles',
        'created_by'
    ];

    protected $casts = [
        'visible_to_roles' => 'array'
    ];

    /**
     * Get the user who created this form
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Check if form is visible to a specific role
     */
    public function isVisibleToRole($role)
    {
        if (!$this->visible_to_roles) {
            return false;
        }

        return in_array($role, $this->visible_to_roles) || in_array('all', $this->visible_to_roles);
    }

    /**
     * Check if current user can view this form
     */
    public function canUserView($user = null)
    {
        if (!$user) {
            $user = auth()->user();
        }

        if (!$user) {
            return false;
        }

        // Company users can see all forms
        if ($user->type == 'company') {
            return true;
        }

        // Check if form is visible to 'all'
        if (in_array('all', $this->visible_to_roles)) {
            return true;
        }

        // Check user roles
        $userRoles = $user->getRoleNames()->toArray();
        foreach ($userRoles as $role) {
            if (in_array($role, $this->visible_to_roles)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Get forms visible to current user
     */
    public static function getVisibleForms()
    {
        $user = auth()->user();

        if (!$user) {
            return collect();
        }

        // If user is company type, show all forms
        if ($user->type == 'company') {
            return self::orderBy('created_at', 'desc')->get();
        }

        // Get user roles
        $userRoles = $user->getRoleNames()->toArray();

        return self::where(function($query) use ($userRoles) {
            // Check for 'all' first
            $query->whereJsonContains('visible_to_roles', 'all');

            // Then check for specific roles
            foreach ($userRoles as $role) {
                $query->orWhereJsonContains('visible_to_roles', $role);
            }
        })->orderBy('created_at', 'desc')->get();
    }

    /**
     * Get file URL
     */
    public function getFileUrlAttribute()
    {
        return asset('storage/' . $this->file_path);
    }
}
