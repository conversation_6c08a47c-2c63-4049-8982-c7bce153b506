<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\ChartOfAccountType;
use App\Models\ChartOfAccountSubType;
use App\Models\ChartOfAccount;
use Illuminate\Support\Facades\DB;

class ChartOfAccountSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // إنشاء أنواع الحسابات الأساسية
        $accountTypes = [
            ['id' => 1, 'name' => 'Assets', 'created_by' => 1],
            ['id' => 2, 'name' => 'Liabilities', 'created_by' => 1],
            ['id' => 3, 'name' => 'Equity', 'created_by' => 1],
            ['id' => 4, 'name' => 'Income', 'created_by' => 1],
            ['id' => 5, 'name' => 'Expenses', 'created_by' => 1],
            ['id' => 6, 'name' => 'Costs of Goods Sold', 'created_by' => 1],
        ];

        foreach ($accountTypes as $type) {
            ChartOfAccountType::updateOrCreate(
                ['id' => $type['id']],
                $type
            );
        }

        // إنشاء الأنواع الفرعية
        $subTypes = [
            // Assets subtypes
            ['id' => 1, 'name' => 'Current Asset', 'type' => 1, 'created_by' => 1],
            ['id' => 2, 'name' => 'Inventory Asset', 'type' => 1, 'created_by' => 1],
            ['id' => 3, 'name' => 'Non-current Asset', 'type' => 1, 'created_by' => 1],
            
            // Liabilities subtypes
            ['id' => 4, 'name' => 'Current Liabilities', 'type' => 2, 'created_by' => 1],
            ['id' => 5, 'name' => 'Long Term Liabilities', 'type' => 2, 'created_by' => 1],
            ['id' => 6, 'name' => 'Share Capital', 'type' => 2, 'created_by' => 1],
            ['id' => 7, 'name' => 'Retained Earnings', 'type' => 2, 'created_by' => 1],
            
            // Equity subtypes
            ['id' => 8, 'name' => 'Owners Equity', 'type' => 3, 'created_by' => 1],
            
            // Income subtypes
            ['id' => 9, 'name' => 'Sales Revenue', 'type' => 4, 'created_by' => 1],
            ['id' => 10, 'name' => 'Other Revenue', 'type' => 4, 'created_by' => 1],
            
            // Costs of Goods Sold subtypes
            ['id' => 11, 'name' => 'Costs of Goods Sold', 'type' => 6, 'created_by' => 1],
            
            // Expenses subtypes
            ['id' => 12, 'name' => 'Payroll Expenses', 'type' => 5, 'created_by' => 1],
            ['id' => 13, 'name' => 'General and Administrative expenses', 'type' => 5, 'created_by' => 1],
        ];

        foreach ($subTypes as $subType) {
            ChartOfAccountSubType::updateOrCreate(
                ['id' => $subType['id']],
                $subType
            );
        }

        // إنشاء الحسابات الأساسية
        $accounts = [
            // Assets
            ['id' => 1, 'name' => 'Checking Account', 'code' => 1060, 'type' => 1, 'sub_type' => 1, 'parent' => 0, 'is_enabled' => 1, 'description' => 'Main checking account', 'created_by' => 1],
            ['id' => 2, 'name' => 'Petty Cash', 'code' => 1065, 'type' => 1, 'sub_type' => 1, 'parent' => 0, 'is_enabled' => 1, 'description' => 'Petty cash account', 'created_by' => 1],
            ['id' => 3, 'name' => 'Account Receivables', 'code' => 1200, 'type' => 1, 'sub_type' => 1, 'parent' => 0, 'is_enabled' => 1, 'description' => 'Customer receivables', 'created_by' => 1],
            ['id' => 4, 'name' => 'Inventory', 'code' => 1510, 'type' => 1, 'sub_type' => 2, 'parent' => 0, 'is_enabled' => 1, 'description' => 'Main inventory account', 'created_by' => 1],
            
            // Liabilities
            ['id' => 5, 'name' => 'Accounts Payable', 'code' => 2000, 'type' => 2, 'sub_type' => 4, 'parent' => 0, 'is_enabled' => 1, 'description' => 'Vendor payables', 'created_by' => 1],
            ['id' => 6, 'name' => 'Accrued Liabilities', 'code' => 2100, 'type' => 2, 'sub_type' => 4, 'parent' => 0, 'is_enabled' => 1, 'description' => 'Accrued expenses', 'created_by' => 1],
            
            // Equity
            ['id' => 7, 'name' => 'Owner Equity', 'code' => 3000, 'type' => 3, 'sub_type' => 8, 'parent' => 0, 'is_enabled' => 1, 'description' => 'Owner equity account', 'created_by' => 1],
            ['id' => 8, 'name' => 'Retained Earnings', 'code' => 3200, 'type' => 3, 'sub_type' => 8, 'parent' => 0, 'is_enabled' => 1, 'description' => 'Retained earnings', 'created_by' => 1],
            
            // Income
            ['id' => 9, 'name' => 'Sales Revenue', 'code' => 4000, 'type' => 4, 'sub_type' => 9, 'parent' => 0, 'is_enabled' => 1, 'description' => 'Main sales revenue', 'created_by' => 1],
            ['id' => 10, 'name' => 'Service Revenue', 'code' => 4100, 'type' => 4, 'sub_type' => 9, 'parent' => 0, 'is_enabled' => 1, 'description' => 'Service income', 'created_by' => 1],
            ['id' => 11, 'name' => 'Other Income', 'code' => 4200, 'type' => 4, 'sub_type' => 10, 'parent' => 0, 'is_enabled' => 1, 'description' => 'Miscellaneous income', 'created_by' => 1],
            
            // Expenses
            ['id' => 12, 'name' => 'Office Expenses', 'code' => 5000, 'type' => 5, 'sub_type' => 13, 'parent' => 0, 'is_enabled' => 1, 'description' => 'General office expenses', 'created_by' => 1],
            ['id' => 13, 'name' => 'Rent Expense', 'code' => 5100, 'type' => 5, 'sub_type' => 13, 'parent' => 0, 'is_enabled' => 1, 'description' => 'Office rent', 'created_by' => 1],
            ['id' => 14, 'name' => 'Utilities Expense', 'code' => 5200, 'type' => 5, 'sub_type' => 13, 'parent' => 0, 'is_enabled' => 1, 'description' => 'Utilities and services', 'created_by' => 1],
            ['id' => 15, 'name' => 'Salary Expense', 'code' => 5300, 'type' => 5, 'sub_type' => 12, 'parent' => 0, 'is_enabled' => 1, 'description' => 'Employee salaries', 'created_by' => 1],
            
            // Cost of Goods Sold
            ['id' => 16, 'name' => 'Cost of Goods Sold', 'code' => 6000, 'type' => 6, 'sub_type' => 11, 'parent' => 0, 'is_enabled' => 1, 'description' => 'Direct costs of products sold', 'created_by' => 1],
        ];

        foreach ($accounts as $account) {
            ChartOfAccount::updateOrCreate(
                ['id' => $account['id']],
                $account
            );
        }

        $this->command->info('Chart of Accounts seeded successfully!');
    }
}
