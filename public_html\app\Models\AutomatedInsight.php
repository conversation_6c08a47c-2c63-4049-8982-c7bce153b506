<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AutomatedInsight extends Model
{
    protected $fillable = [
        'insight_type',
        'title',
        'description',
        'priority',
        'warehouse_id',
        'related_customer_id',
        'related_product_id',
        'is_read',
        'is_actionable',
        'expires_at',
        'created_by',
    ];

    protected $casts = [
        'is_read' => 'boolean',
        'is_actionable' => 'boolean',
        'expires_at' => 'date',
    ];

    /**
     * أنواع الرؤى المتاحة
     */
    public static $insightTypes = [
        'opportunity' => 'فرصة',
        'warning' => 'تحذير',
        'achievement' => 'إنجاز',
        'recommendation' => 'توصية',
    ];

    /**
     * مستويات الأولوية
     */
    public static $priorities = [
        'high' => 'عالية',
        'medium' => 'متوسطة',
        'low' => 'منخفضة',
    ];

    /**
     * العلاقة مع المستودع
     */
    public function warehouse(): BelongsTo
    {
        return $this->belongsTo(\App\Models\warehouse::class);
    }

    /**
     * العلاقة مع العميل المرتبط
     */
    public function relatedCustomer(): BelongsTo
    {
        return $this->belongsTo(Customer::class, 'related_customer_id');
    }

    /**
     * العلاقة مع المنتج المرتبط
     */
    public function relatedProduct(): BelongsTo
    {
        return $this->belongsTo(ProductService::class, 'related_product_id');
    }

    /**
     * العلاقة مع المستخدم المنشئ
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * الحصول على اسم نوع الرؤية بالعربية
     */
    public function getInsightTypeNameAttribute(): string
    {
        return self::$insightTypes[$this->insight_type] ?? $this->insight_type;
    }

    /**
     * الحصول على اسم الأولوية بالعربية
     */
    public function getPriorityNameAttribute(): string
    {
        return self::$priorities[$this->priority] ?? $this->priority;
    }

    /**
     * الحصول على لون نوع الرؤية
     */
    public function getInsightColorAttribute(): string
    {
        return match($this->insight_type) {
            'opportunity' => 'success',
            'warning' => 'warning',
            'achievement' => 'primary',
            'recommendation' => 'info',
            default => 'secondary',
        };
    }

    /**
     * الحصول على لون الأولوية
     */
    public function getPriorityColorAttribute(): string
    {
        return match($this->priority) {
            'high' => 'danger',
            'medium' => 'warning',
            'low' => 'success',
            default => 'secondary',
        };
    }

    /**
     * الحصول على أيقونة نوع الرؤية
     */
    public function getInsightIconAttribute(): string
    {
        return match($this->insight_type) {
            'opportunity' => 'fas fa-lightbulb',
            'warning' => 'fas fa-exclamation-triangle',
            'achievement' => 'fas fa-trophy',
            'recommendation' => 'fas fa-thumbs-up',
            default => 'fas fa-info-circle',
        };
    }

    /**
     * تحديد ما إذا كانت الرؤية منتهية الصلاحية
     */
    public function getIsExpiredAttribute(): bool
    {
        return $this->expires_at && $this->expires_at->isPast();
    }

    /**
     * فلترة الرؤى غير المقروءة
     */
    public function scopeUnread($query)
    {
        return $query->where('is_read', false);
    }

    /**
     * فلترة الرؤى المقروءة
     */
    public function scopeRead($query)
    {
        return $query->where('is_read', true);
    }

    /**
     * فلترة الرؤى القابلة للتنفيذ
     */
    public function scopeActionable($query)
    {
        return $query->where('is_actionable', true);
    }

    /**
     * فلترة الرؤى حسب النوع
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('insight_type', $type);
    }

    /**
     * فلترة الرؤى حسب الأولوية
     */
    public function scopeOfPriority($query, $priority)
    {
        return $query->where('priority', $priority);
    }

    /**
     * فلترة الرؤى حسب المستودع
     */
    public function scopeForWarehouse($query, $warehouseId)
    {
        if ($warehouseId) {
            return $query->where('warehouse_id', $warehouseId);
        }
        return $query;
    }

    /**
     * فلترة الرؤى حسب المنشئ
     */
    public function scopeForCreator($query, $creatorId)
    {
        return $query->where('created_by', $creatorId);
    }

    /**
     * فلترة الرؤى غير منتهية الصلاحية
     */
    public function scopeActive($query)
    {
        return $query->where(function($q) {
            $q->whereNull('expires_at')
              ->orWhere('expires_at', '>=', now());
        });
    }

    /**
     * فلترة الرؤى منتهية الصلاحية
     */
    public function scopeExpired($query)
    {
        return $query->where('expires_at', '<', now());
    }

    /**
     * ترتيب حسب الأولوية (العالية أولاً)
     */
    public function scopeByPriority($query)
    {
        return $query->orderByRaw("FIELD(priority, 'high', 'medium', 'low')");
    }

    /**
     * ترتيب حسب التاريخ (الأحدث أولاً)
     */
    public function scopeLatest($query)
    {
        return $query->orderBy('created_at', 'desc');
    }

    /**
     * تحديد الرؤية كمقروءة
     */
    public function markAsRead(): bool
    {
        return $this->update(['is_read' => true]);
    }

    /**
     * تحديد الرؤية كغير مقروءة
     */
    public function markAsUnread(): bool
    {
        return $this->update(['is_read' => false]);
    }
}
