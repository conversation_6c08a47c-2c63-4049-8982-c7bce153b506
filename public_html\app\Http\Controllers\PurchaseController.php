<?php

namespace App\Http\Controllers;

use App\Models\BankAccount;
use App\Models\Bill;
use App\Models\BillAccount;
use App\Models\BillProduct;
use App\Models\ChartOfAccount;
use App\Models\CustomField;
use App\Models\ProductService;
use App\Models\ProductServiceCategory;
use App\Models\Purchase;
use App\Models\PurchaseAccount;
use App\Models\PurchaseProduct;
use App\Models\PurchasePayment;
use App\Models\Shift;
use App\Models\StockReport;
use App\Models\Transaction;
use App\Models\Vender;
use App\Models\User;
use App\Models\Utility;
use App\Models\WarehouseProduct;
use App\Models\WarehouseTransfer;
use Illuminate\Support\Facades\Crypt;
use App\Models\warehouse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

class PurchaseController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {

        $vender = Vender::where('created_by', '=', \Auth::user()->creatorId())->get()->pluck('name', 'id');
        $vender->prepend('Select Vendor', '');
        $status = Purchase::$statues;
        //get the current open shift for the user
        $warehouse = Auth::user()->warehouse_id;
        $openShift = Shift::whereNull('closed_at')->where('warehouse_id', $warehouse)->first();

        // Verificar si existe un turno abierto antes de acceder a su ID
        if ($openShift) {
            $purchases = Purchase::where('created_by', '=', \Auth::user()->creatorId())
                ->where('shift_id', $openShift->id)
                ->with(['vender', 'category', 'warehouse'])
                ->get();
        } else {
            // Si no hay turno abierto, mostrar todas las compras sin filtrar por shift_id
            $purchases = Purchase::where('created_by', '=', \Auth::user()->creatorId())
                ->with(['vender', 'category', 'warehouse'])
                ->get();
        }

        return view('purchase.index', compact('purchases', 'status','vender'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create($vendorId = 0)
    {
        if (\Auth::user()->can('create purchase')) {
            $customFields = CustomField::where('created_by', '=', \Auth::user()->creatorId())->where('module', '=', 'purchase')->get();

            // Get the current user's warehouse ID
            $currentWarehouseId = \Auth::user()->warehouse_id;

            // Get categories based on warehouse
            $categoryQuery = ProductServiceCategory::where('created_by', \Auth::user()->creatorId())->where('type', 'expense');

            // Filter categories by warehouse if user has a warehouse assigned
            if ($currentWarehouseId) {
                $categoryQuery->where(function($query) use ($currentWarehouseId) {
                    $query->whereNull('warehouse_ids')
                          ->orWhere('warehouse_ids', '=', '')
                          ->orWhereRaw('JSON_CONTAINS(warehouse_ids, ?)', [json_encode((string)$currentWarehouseId)]);
                });
            }

            $category = $categoryQuery->get()->pluck('name', 'id');
            $category->prepend('Select Category', '');

            $purchase_number = \Auth::user()->purchaseNumberFormat($this->purchaseNumber());
            $venders     = Vender::where('created_by', \Auth::user()->creatorId())->get()->pluck('name', 'id');
            $venders->prepend('Select Vender', '');

            if (\Auth::user()->type == 'company') {
                $warehouse     = warehouse::where('created_by', \Auth::user()->creatorId())->get()->pluck('name', 'id');
            } else {
                $warehouse     = warehouse::where('id', \Auth::user()->warehouse_id)->get()->pluck('name', 'id');
            }

            $warehouse->prepend('Select Warehouse', '');

            $product_services = ProductService::where('created_by', \Auth::user()->creatorId())->where('type', '!=', 'service')->get()->pluck('name', 'id');
            $product_services->prepend('-select Item', '');
            $product_services_sku = ProductService::where('created_by', \Auth::user()->creatorId())->where('type', '!=', 'service')->get()->pluck('sku', 'id');
            $product_services_sku->prepend('-with SKU', '');

            //new to purchase section
            $chartAccounts = ChartOfAccount::select(\DB::raw('CONCAT(code, " - ", name) AS code_name, id'))
                ->where('created_by', \Auth::user()->creatorId())->get()
                ->pluck('code_name', 'id');
            $chartAccounts->prepend('Select Account', '');

            $subAccounts = ChartOfAccount::select('chart_of_accounts.id', 'chart_of_accounts.code', 'chart_of_accounts.name', 'chart_of_account_parents.account');
            $subAccounts->leftjoin('chart_of_account_parents', 'chart_of_accounts.parent', 'chart_of_account_parents.id');
            $subAccounts->where('chart_of_accounts.parent', '!=', 0);
            $subAccounts->where('chart_of_accounts.created_by', \Auth::user()->creatorId());
            $subAccounts = $subAccounts->get()->toArray();

            return view('purchase.create', compact('venders', 'purchase_number', 'product_services','product_services_sku', 'category', 'customFields', 'vendorId', 'warehouse', 'chartAccounts', 'subAccounts'));
        } else {
            return response()->json(['error' => __('Permission denied.')], 401);
        }
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {

        //the current open shift for the user
        $warehouse = Auth::user()->warehouse_id;

        $openShift = Shift::whereNull('closed_at')->where('warehouse_id', $warehouse)->first();

        if (\Auth::user()->can('create purchase')) {
            $validator = \Validator::make(
                $request->all(),
                [
                    'vender_id' => 'required',
                    'warehouse_id' => 'required',
                    'purchase_date' => 'required',
                    'category_id' => 'required',
                    'items' => 'required',
                    'order_number' => 'required|numeric',
                ]
            );



            if ($validator->fails()) {
                $messages = $validator->getMessageBag();

                return redirect()->back()->with('error', $messages->first());
            }


            // if (!empty($request->items) && empty($request->items[0]['chart_account_id'])  && !empty($request->items[0]['amount'])) {
            //     $accountValidator = \Validator::make(
            //         $request->all(),
            //         [
            //             'chart_account_id' => 'required'
            //         ]
            //     );
            //     if ($accountValidator->fails()) {
            //         $messages2 = $accountValidator->getMessageBag();
            //         return redirect()->back()->with('error', $messages2->first());
            //     }
            // }

            $purchase                 = new Purchase();
            $purchase->purchase_id    = $this->purchaseNumber();
            $purchase->vender_id      = $request->vender_id;
            $purchase->warehouse_id      = $request->warehouse_id;
            $purchase->purchase_date  = $request->purchase_date;
            $purchase->purchase_number   = !empty($request->purchase_number) ? $request->purchase_number : 0;
            $purchase->status         =  0;
            //            $purchase->discount_apply = isset($request->discount_apply) ? 1 : 0;
            $purchase->category_id    = $request->category_id;
            $purchase->created_by     = \Auth::user()->creatorId();
            // Verificar si existe un turno abierto antes de asignar su ID
            if ($openShift) {
                $purchase->shift_id = $openShift->id;
            }
            $purchase->order_number   = $request->order_number;
            //upload invoice image
            //validate the invoice_image file if it is present with right mime type pdf,jpeg,png,jpg,gif
            if ($request->hasFile('invoice_image') && $request->file('invoice_image')->isValid([
                'mimes:pdf,jpeg,png,jpg,gif,docx,doc,bmp,tiff,webp',
                'max:102400'
            ])) {
                $file = $request->file('invoice_image');
                $fileName = time() . "_" . $file->getClientOriginalName();
                $filePath = $file->storeAs('uploads/purchase', $fileName);
                $purchase->invoice_image = $fileName;
            }
            $purchase->save();

            $products = $request->items;
            $total_amount = 0;

            for ($i = 0; $i < count($products); $i++) {
                $purchaseProduct              = new PurchaseProduct();
                $purchaseProduct->purchase_id     = $purchase->id;
                $purchaseProduct->product_id  = $products[$i]['item'];
                $purchaseProduct->quantity    = $products[$i]['quantity'];
                $purchaseProduct->tax         = $products[$i]['tax'];
                //                $purchaseProduct->discount    = isset($products[$i]['discount']) ? $products[$i]['discount'] : 0;
                $purchaseProduct->discount    = $products[$i]['discount'];
                $purchaseProduct->price       = $products[$i]['price'];
                $purchaseProduct->description = $products[$i]['description'];
                $purchaseProduct->save();

                //inventory management (Quantity)
                Utility::total_quantity('plus', $purchaseProduct->quantity, $purchaseProduct->product_id);

                //Product Stock Report
                $type = 'purchase';
                $type_id = $purchase->id;
                $description = $products[$i]['quantity'] . '  ' . __(' quantity add in purchase') . ' ' . \Auth::user()->purchaseNumberFormat($purchase->purchase_id);
                Utility::addProductStock($products[$i]['item'], $products[$i]['quantity'], $type, $description, $type_id);

                //Warehouse Stock Report
                if (isset($products[$i]['item']) && !empty($request->warehouse_id)) {
                    // Registrar información para depuración
                    \Log::info('Actualizando stock de almacén', [
                        'product_id' => $products[$i]['item'],
                        'quantity' => $products[$i]['quantity'],
                        'warehouse_id' => $request->warehouse_id
                    ]);

                    Utility::addWarehouseStock($products[$i]['item'], $products[$i]['quantity'], $request->warehouse_id);
                } else {
                    \Log::warning('No se pudo actualizar el stock del almacén', [
                        'product_id' => $products[$i]['item'] ?? 'no definido',
                        'warehouse_id' => $request->warehouse_id ?? 'no definido'
                    ]);
                }

                $purchaseTotal = 0;
                if (!empty($products[$i]['chart_account_id'])) {
                    $purchaseAccount                    = new PurchaseAccount();
                    $purchaseAccount->chart_account_id  = $products[$i]['chart_account_id'];
                    $purchaseAccount->price             = $products[$i]['amount'] ? $products[$i]['amount'] : 0;
                    $purchaseAccount->description       = $products[$i]['description'];
                    $purchaseAccount->type              = 'Purchase';
                    $purchaseAccount->ref_id            = $purchase->id;
                    $purchaseAccount->save();
                    $purchaseTotal = $purchaseAccount->price;
                }
            }

            //================ new purchase code here ==============
            if (!empty($request->items) && !empty($request->items[0]['chart_account_id'])  && !empty($request->items[0]['amount']) )
            {
                $accountValidator = \Validator::make(
                    $request->all(), [
                        'chart_account_id' => 'required'
                    ]
                );
                if ($accountValidator->fails()) {
                    $messages2 = $accountValidator->getMessageBag();
                    return redirect()->back()->with('error', $messages2->first());
                }

            }

            // Automatic bill creation has been disabled as requested
            // The code that automatically created a bill when a purchase is created has been removed


            return redirect()->route('dashboard', $purchase->id)->with('success', __('Purchase successfully created.'));
        } else {
            return redirect()->back()->with('error', __('Permission denied.'));
        }
    }

    //old store
    // public function store(Request $request)
    // {

    //     //the current open shift for the user
    //     $warehouse = Auth::user()->warehouse_id;

    //     $openShift = Shift::whereNull('closed_at')->where('warehouse_id', $warehouse)->first();

    //     if (\Auth::user()->can('create purchase')) {
    //         $validator = \Validator::make(
    //             $request->all(),
    //             [
    //                 'vender_id' => 'required',
    //                 'warehouse_id' => 'required',
    //                 'purchase_date' => 'required',
    //                 'category_id' => 'required',
    //                 'items' => 'required',
    //             ]
    //         );
    //         if ($validator->fails()) {
    //             $messages = $validator->getMessageBag();

    //             return redirect()->back()->with('error', $messages->first());
    //         }
    //         $purchase                 = new Purchase();
    //         $purchase->purchase_id    = $this->purchaseNumber();
    //         $purchase->vender_id      = $request->vender_id;
    //         $purchase->warehouse_id      = $request->warehouse_id;
    //         $purchase->purchase_date  = $request->purchase_date;
    //         $purchase->purchase_number   = !empty($request->purchase_number) ? $request->purchase_number : 0;
    //         $purchase->status         =  0;
    //         //            $purchase->discount_apply = isset($request->discount_apply) ? 1 : 0;
    //         $purchase->category_id    = $request->category_id;
    //         $purchase->created_by     = \Auth::user()->creatorId();
    //         $purchase->shift_id    = $openShift->id;
    //         $purchase->save();

    //         $products = $request->items;

    //         for ($i = 0; $i < count($products); $i++) {
    //             $purchaseProduct              = new PurchaseProduct();
    //             $purchaseProduct->purchase_id     = $purchase->id;
    //             $purchaseProduct->product_id  = $products[$i]['item'];
    //             $purchaseProduct->quantity    = $products[$i]['quantity'];
    //             $purchaseProduct->tax         = $products[$i]['tax'];
    //             //                $purchaseProduct->discount    = isset($products[$i]['discount']) ? $products[$i]['discount'] : 0;
    //             $purchaseProduct->discount    = $products[$i]['discount'];
    //             $purchaseProduct->price       = $products[$i]['price'];
    //             $purchaseProduct->description = $products[$i]['description'];
    //             $purchaseProduct->save();

    //             //inventory management (Quantity)
    //             Utility::total_quantity('plus', $purchaseProduct->quantity, $purchaseProduct->product_id);

    //             //Product Stock Report
    //             $type = 'purchase';
    //             $type_id = $purchase->id;
    //             $description = $products[$i]['quantity'] . '  ' . __(' quantity add in purchase') . ' ' . \Auth::user()->purchaseNumberFormat($purchase->purchase_id);
    //             Utility::addProductStock($products[$i]['item'], $products[$i]['quantity'], $type, $description, $type_id);

    //             //Warehouse Stock Report
    //             if (isset($products[$i]['item'])) {
    //                 Utility::addWarehouseStock($products[$i]['item'], $products[$i]['quantity'], $request->warehouse_id);
    //             }
    //         }

    //         return redirect()->route('purchase.index', $purchase->id)->with('success', __('Purchase successfully created.'));
    //     } else {
    //         return redirect()->back()->with('error', __('Permission denied.'));
    //     }
    // }


    public function show($ids)
    {

        if (\Auth::user()->can('show purchase')) {
            try {
                $id       = Crypt::decrypt($ids);
            } catch (\Throwable $th) {
                return redirect()->back()->with('error', __('Purchase Not Found.'));
            }

            $id   = Crypt::decrypt($ids);
            $purchase = Purchase::find($id);

            if ($purchase->created_by == \Auth::user()->creatorId()) {

                $purchasePayment = PurchasePayment::where('purchase_id', $purchase->id)->first();
                $vendor      = $purchase->vender;
                $iteams      = $purchase->items;

                return view('purchase.view', compact('purchase', 'vendor', 'iteams', 'purchasePayment'));
            } else {
                return redirect()->back()->with('error', __('Permission denied.'));
            }
        } else {
            return redirect()->back()->with('error', __('Permission denied.'));
        }
    }

    public function edit($idsd)
    {
        if (\Auth::user()->can('edit purchase')) {

            $idwww   = Crypt::decrypt($idsd);
            $purchase     = Purchase::find($idwww);

            // Get the current user's warehouse ID
            $currentWarehouseId = \Auth::user()->warehouse_id;

            // Get categories based on warehouse
            $categoryQuery = ProductServiceCategory::where('created_by', \Auth::user()->creatorId())->where('type', 'expense');

            // Filter categories by warehouse if user has a warehouse assigned
            if ($currentWarehouseId) {
                $categoryQuery->where(function($query) use ($currentWarehouseId) {
                    $query->whereNull('warehouse_ids')
                          ->orWhere('warehouse_ids', '=', '')
                          ->orWhereRaw('JSON_CONTAINS(warehouse_ids, ?)', [json_encode((string)$currentWarehouseId)]);
                });
            }

            $category = $categoryQuery->get()->pluck('name', 'id');
            $category->prepend('Select Category', '');
            $warehouse     = warehouse::where('created_by', \Auth::user()->creatorId())->get()->pluck('name', 'id');

            $purchase_number      = \Auth::user()->purchaseNumberFormat($purchase->purchase_id);
            $venders          = Vender::where('created_by', \Auth::user()->creatorId())->get()->pluck('name', 'id');
            $product_services = ProductService::where('created_by', \Auth::user()->creatorId())->where('type', '!=', 'service')->get()->pluck('name', 'id');
            $product_services->prepend('-select Item', '');
            $product_services_sku = ProductService::where('created_by', \Auth::user()->creatorId())->where('type', '!=', 'service')->get()->pluck('sku', 'id');
            $product_services_sku->prepend('-with SKU', '');

            //new to purchase section
            $purchaseAccounts = PurchaseAccount::where('ref_id', $purchase->id)->first();
            $chartAccounts = ChartOfAccount::select(\DB::raw('CONCAT(code, " - ", name) AS code_name, id'))
                ->where('created_by', \Auth::user()->creatorId())->get()
                ->pluck('code_name', 'id');
            $chartAccounts->prepend('Select Account', '');

            $subAccounts = ChartOfAccount::select('chart_of_accounts.id', 'chart_of_accounts.code', 'chart_of_accounts.name', 'chart_of_account_parents.account');
            $subAccounts->leftjoin('chart_of_account_parents', 'chart_of_accounts.parent', 'chart_of_account_parents.id');
            $subAccounts->where('chart_of_accounts.parent', '!=', 0);
            $subAccounts->where('chart_of_accounts.created_by', \Auth::user()->creatorId());
            $subAccounts = $subAccounts->get()->toArray();

            return view('purchase.edit', compact(
                'venders',
                'product_services',
                'product_services_sku',
                'purchase',
                'warehouse',
                'purchase_number',
                'category',
                'chartAccounts',
                'subAccounts',
                'purchaseAccounts'
            ));
        } else {
            return response()->json(['error' => __('Permission denied.')], 401);
        }
    }


    //old edit
    // public function edit($idsd)
    // {
    //     if (\Auth::user()->can('edit purchase')) {

    //         $idwww   = Crypt::decrypt($idsd);
    //         $purchase     = Purchase::find($idwww);
    //         $category = ProductServiceCategory::where('created_by', \Auth::user()->creatorId())->where('type', 'expense')->get()->pluck('name', 'id');
    //         $category->prepend('Select Category', '');
    //         $warehouse     = warehouse::where('created_by', \Auth::user()->creatorId())->get()->pluck('name', 'id');

    //         $purchase_number      = \Auth::user()->purchaseNumberFormat($purchase->purchase_id);
    //         $venders          = Vender::where('created_by', \Auth::user()->creatorId())->get()->pluck('name', 'id');
    //         $product_services = ProductService::where('created_by', \Auth::user()->creatorId())->where('type', '!=', 'service')->get()->pluck('name', 'id');

    //         return view('purchase.edit', compact('venders', 'product_services', 'purchase', 'warehouse', 'purchase_number', 'category'));
    //     } else {
    //         return response()->json(['error' => __('Permission denied.')], 401);
    //     }
    // }

    //new update
    // public function update(Request $request, Purchase $purchase)
    // {


    //     if (\Auth::user()->can('edit purchase')) {

    //         if ($purchase->created_by == \Auth::user()->creatorId()) {
    //             $validator = \Validator::make(
    //                 $request->all(),
    //                 [
    //                     'vender_id' => 'required',
    //                     'purchase_date' => 'required',
    //                     'items' => 'required',
    //                     'order_number' => 'required|numeric',
    //                 ]
    //             );
    //             if ($validator->fails()) {
    //                 $messages = $validator->getMessageBag();

    //                 return redirect()->route('purchase.index')->with('error', $messages->first());
    //             }
    //             $purchase->vender_id      = $request->vender_id;
    //             $purchase->purchase_date      = $request->purchase_date;
    //             $purchase->category_id    = $request->category_id;
    //             $purchase->order_number   = $request->order_number;
    //             //upload invoice image
    //             //validate the invoice_image file if it is present with right mime type pdf,jpeg,png,jpg,gif
    //             if ($request->hasFile('invoice_image') && $request->file('invoice_image')->isValid([
    //                 'mimes:pdf,jpeg,png,jpg,gif',
    //                 'max:2048'
    //             ])) {
    //                 $file = $request->file('invoice_image');
    //                 $fileName = time() . "_" . $file->getClientOriginalName();
    //                 $filePath = $file->storeAs('uploads/purchase', $fileName, 'public');
    //                 $purchase->invoice_image = $fileName;
    //             }
    //             $purchase->save();
    //             $products = $request->items;

    //             for ($i = 0; $i < count($products); $i++) {
    //                 $purchaseProduct = PurchaseProduct::find($products[$i]['id']);

    //                 if ($purchaseProduct == null) {
    //                     $purchaseProduct             = new PurchaseProduct();
    //                     $purchaseProduct->purchase_id    = $purchase->id;

    //                     Utility::total_quantity('plus', $products[$i]['quantity'], $products[$i]['item']);
    //                     $old_qty = 0;
    //                 } else {
    //                     $old_qty = $purchaseProduct->quantity;
    //                     Utility::total_quantity('minus', $purchaseProduct->quantity, $purchaseProduct->product_id);
    //                 }

    //                 if (isset($products[$i]['item'])) {
    //                     $purchaseProduct->product_id = $products[$i]['item'];
    //                 }

    //                 $purchaseProduct->quantity    = $products[$i]['quantity'];
    //                 $purchaseProduct->tax         = $products[$i]['tax'];
    //                 $purchaseProduct->discount    = $products[$i]['discount'];
    //                 $purchaseProduct->price       = $products[$i]['price'];
    //                 $purchaseProduct->description = $products[$i]['description'];
    //                 $purchaseProduct->save();

    //                 $purchaseTotal = 0;
    //                 $total_amount = 0;
    //                 if (!empty($products[$i]['chart_account_id'])) {
    //                     $purchaseAccount = PurchaseAccount::find($products[$i]['account_id']);

    //                     if ($purchaseAccount == null) {
    //                         $purchaseAccount                    = new PurchaseAccount();
    //                         $purchaseAccount->chart_account_id = $products[$i]['chart_account_id'];
    //                     } else {
    //                         $purchaseAccount->chart_account_id = $products[$i]['chart_account_id'];
    //                     }
    //                     $purchaseAccount->price             = $products[$i]['amount'] ? $products[$i]['amount'] : 0;
    //                     $purchaseAccount->description       = $products[$i]['description'];
    //                     $purchaseAccount->type              = 'Purchase';
    //                     $purchaseAccount->ref_id            = $purchase->id;
    //                     $purchaseAccount->save();
    //                     $purchaseTotal = $purchaseAccount->price;
    //                 }

    //                 if ($products[$i]['id'] > 0) {
    //                     Utility::total_quantity('plus', $products[$i]['quantity'], $purchaseProduct->product_id);
    //                 }

    //                 //Product Stock Report
    //                 $type = 'purchase';
    //                 $type_id = $purchase->id;
    //                 StockReport::where('type', '=', 'purchase')->where('type_id', '=', $purchase->id)->delete();
    //                 $description = $products[$i]['quantity'] . '  ' . __(' quantity add in purchase') . ' ' . \Auth::user()->purchaseNumberFormat($purchase->purchase_id);

    //                 if (isset($products[$i]['item'])) {
    //                     Utility::addProductStock($products[$i]['item'], $products[$i]['quantity'], $type, $description, $type_id);
    //                 }

    //                 //Warehouse Stock Report
    //                 $new_qty = $purchaseProduct->quantity;
    //                 $total_qty = $new_qty - $old_qty;
    //                 if (isset($products[$i]['item'])) {

    //                     Utility::addWarehouseStock($products[$i]['item'], $total_qty, $request->warehouse_id);
    //                 }
    //                 $total_amount += ($purchaseProduct->quantity * $purchaseProduct->price) + $purchaseTotal;
    //             }

    //             if (!empty($request->chart_account_id)) {
    //                 $purchaseaccount = ProductServiceCategory::find($request->category_id);
    //                 $chart_account = ChartOfAccount::find($purchaseaccount->chart_account_id);
    //                 $purchaseAccount                    = new PurchaseAccount();
    //                 $purchaseAccount->chart_account_id  = $chart_account['id'];
    //                 $purchaseAccount->price             = $total_amount;
    //                 $purchaseAccount->description       = $request->description;
    //                 $purchaseAccount->type              = 'Purchase Category';
    //                 $purchaseAccount->ref_id            = $purchase->id;
    //                 $purchaseAccount->save();
    //             }

    //             return redirect()->route('dashboard')->with('success', __('Purchase successfully updated.'));
    //         } else {
    //             return redirect()->back()->with('error', __('Permission denied.'));
    //         }
    //     } else {
    //         return redirect()->back()->with('error', __('Permission denied.'));
    //     }
    // }

    //old update
    public function update(Request $request, Purchase $purchase)
    {


        if (\Auth::user()->can('edit purchase')) {

            if ($purchase->created_by == \Auth::user()->creatorId()) {
                $validator = \Validator::make(
                    $request->all(),
                    [
                        'vender_id' => 'required',
                        'purchase_date' => 'required',
                        'items' => 'required',
                        'order_number' => 'required|numeric',
                    ]
                );
                if ($validator->fails()) {
                    $messages = $validator->getMessageBag();

                    return redirect()->route('purchase.index')->with('error', $messages->first());
                }
                $purchase->vender_id      = $request->vender_id;
                $purchase->purchase_date  = $request->purchase_date;
                $purchase->category_id    = $request->category_id;
                $purchase->order_number   = $request->order_number;

                // Handle invoice image upload if provided
                if ($request->hasFile('invoice_image') && $request->file('invoice_image')->isValid()) {
                    $file = $request->file('invoice_image');
                    $fileName = time() . "_" . $file->getClientOriginalName();
                    $filePath = $file->storeAs('uploads/purchase', $fileName);
                    $purchase->invoice_image = $fileName;
                }

                $purchase->save();
                $products = $request->items;

                for ($i = 0; $i < count($products); $i++) {
                    $purchaseProduct = PurchaseProduct::find($products[$i]['id']);

                    if ($purchaseProduct == null) {
                        $purchaseProduct             = new PurchaseProduct();
                        $purchaseProduct->purchase_id    = $purchase->id;

                        Utility::total_quantity('plus', $products[$i]['quantity'], $products[$i]['item']);
                        $old_qty = 0;
                    } else {
                        $old_qty = $purchaseProduct->quantity;
                        Utility::total_quantity('minus', $purchaseProduct->quantity, $purchaseProduct->product_id);
                    }

                    if (isset($products[$i]['item'])) {
                        $purchaseProduct->product_id = $products[$i]['item'];
                    }

                    $purchaseProduct->quantity    = $products[$i]['quantity'];
                    $purchaseProduct->tax         = $products[$i]['tax'];
                    $purchaseProduct->discount    = $products[$i]['discount'];
                    $purchaseProduct->price       = $products[$i]['price'];
                    $purchaseProduct->description = $products[$i]['description'];
                    $purchaseProduct->save();

                    if ($products[$i]['id'] > 0) {
                        Utility::total_quantity('plus', $products[$i]['quantity'], $purchaseProduct->product_id);
                    }

                    //Product Stock Report
                    $type = 'purchase';
                    $type_id = $purchase->id;
                    StockReport::where('type', '=', 'purchase')->where('type_id', '=', $purchase->id)->delete();
                    $description = $products[$i]['quantity'] . '  ' . __(' quantity add in purchase') . ' ' . \Auth::user()->purchaseNumberFormat($purchase->purchase_id);

                    if (isset($products[$i]['item'])) {
                        Utility::addProductStock($products[$i]['item'], $products[$i]['quantity'], $type, $description, $type_id);
                    }

                    //Warehouse Stock Report
                    $new_qty = $purchaseProduct->quantity;
                    $total_qty = $new_qty - $old_qty;
                    if (isset($products[$i]['item']) && !empty($request->warehouse_id)) {
                        // Registrar información para depuración
                        \Log::info('Actualizando stock de almacén en update', [
                            'product_id' => $products[$i]['item'],
                            'quantity' => $total_qty,
                            'warehouse_id' => $request->warehouse_id,
                            'old_qty' => $old_qty,
                            'new_qty' => $new_qty
                        ]);

                        Utility::addWarehouseStock($products[$i]['item'], $total_qty, $request->warehouse_id);
                    } else {
                        \Log::warning('No se pudo actualizar el stock del almacén en update', [
                            'product_id' => $products[$i]['item'] ?? 'no definido',
                            'warehouse_id' => $request->warehouse_id ?? 'no definido'
                        ]);
                    }
                }

                return redirect()->route('purchase.index')->with('success', __('Purchase successfully updated.'));
            } else {
                return redirect()->back()->with('error', __('Permission denied.'));
            }
        } else {
            return redirect()->back()->with('error', __('Permission denied.'));
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\Purchase  $purchase
     * @return \Illuminate\Http\Response
     */
    public function destroy(Purchase $purchase)
    {
        if (\Auth::user()->can('delete purchase')) {
            if ($purchase->created_by == \Auth::user()->creatorId()) {
                $purchase_products = PurchaseProduct::where('purchase_id', $purchase->id)->get();

                $purchasepayments = $purchase->payments;
                foreach ($purchasepayments as $key => $value) {
                    $purchasepayment = PurchasePayment::find($value->id)->first();
                    $purchasepayment->delete();
                }

                foreach ($purchase_products as $purchase_product) {
                    $warehouse_qty = WarehouseProduct::where('warehouse_id', $purchase->warehouse_id)->where('product_id', $purchase_product->product_id)->first();

                    $warehouse_transfers = WarehouseTransfer::where('product_id', $purchase_product->product_id)->where('from_warehouse', $purchase->warehouse_id)->get();
                    foreach ($warehouse_transfers as $warehouse_transfer) {
                        $temp = WarehouseProduct::where('warehouse_id', $warehouse_transfer->to_warehouse)->first();
                        if ($temp) {
                            $temp->quantity = $temp->quantity - $warehouse_transfer->quantity;
                            if ($temp->quantity > 0) {
                                $temp->save();
                            } else {
                                $temp->delete();
                            }
                        }
                    }
                    if (!empty($warehouse_qty)) {
                        $warehouse_qty->quantity = $warehouse_qty->quantity - $purchase_product->quantity;
                        if ($warehouse_qty->quantity > 0) {
                            $warehouse_qty->save();
                        } else {
                            $warehouse_qty->delete();
                        }
                    }
                    $product_qty = ProductService::where('id', $purchase_product->product_id)->first();
                    if (!empty($product_qty)) {
                        $product_qty->quantity = $product_qty->quantity - $purchase_product->quantity;
                        $product_qty->save();
                    }
                    $purchase_product->delete();
                }

                $purchase->delete();
                PurchaseProduct::where('purchase_id', '=', $purchase->id)->delete();


                return redirect()->route('purchase.index')->with('success', __('Purchase successfully deleted.'));
            } else {
                return redirect()->back()->with('error', __('Permission denied.'));
            }
        } else {
            return redirect()->back()->with('error', __('Permission denied.'));
        }
    }

    function purchaseNumber()
    {
        $latest = Purchase::where('created_by', '=', \Auth::user()->creatorId())->latest()->first();
        if (!$latest) {
            return 1;
        }

        return $latest->purchase_id + 1;
    }
    public function sent($id)
    {
        if (\Auth::user()->can('send purchase')) {
            $purchase            = Purchase::where('id', $id)->first();
            $purchase->send_date = date('Y-m-d');
            $purchase->status    = 1;
            $purchase->save();

            $vender = Vender::where('id', $purchase->vender_id)->first();

            $purchase->name = !empty($vender) ? $vender->name : '';
            $purchase->purchase = \Auth::user()->purchaseNumberFormat($purchase->purchase_id);

            $purchaseId    = Crypt::encrypt($purchase->id);
            $purchase->url = route('purchase.pdf', $purchaseId);

            Utility::userBalance('vendor', $vender->id, $purchase->getTotal(), 'credit');

            $vendorArr = [
                'vender_bill_name' => $purchase->name,
                'vender_bill_number' => $purchase->purchase,
                'vender_bill_url' => $purchase->url,

            ];
            $resp = \App\Models\Utility::sendEmailTemplate('vender_bill_sent', [$vender->id => $vender->email], $vendorArr);

            return redirect()->back()->with('success', __('Purchase successfully sent.') . (($resp['is_success'] == false && !empty($resp['error'])) ? '<br> <span class="text-danger">' . $resp['error'] . '</span>' : ''));
        } else {
            return redirect()->back()->with('error', __('Permission denied.'));
        }
    }

    public function resent($id)
    {

        if (\Auth::user()->can('send purchase')) {
            $purchase = Purchase::where('id', $id)->first();

            $vender = Vender::where('id', $purchase->vender_id)->first();

            $purchase->name = !empty($vender) ? $vender->name : '';
            $purchase->purchase = \Auth::user()->purchaseNumberFormat($purchase->purchase_id);

            $purchaseId    = Crypt::encrypt($purchase->id);
            $purchase->url = route('purchase.pdf', $purchaseId);
            //

            // Send Email
            //        $setings = Utility::settings();
            //
            //        if($setings['bill_resend'] == 1)
            //        {
            //            $bill = Bill::where('id', $id)->first();
            //            $vender = Vender::where('id', $bill->vender_id)->first();
            //            $bill->name = !empty($vender) ? $vender->name : '';
            //            $bill->bill = \Auth::user()->billNumberFormat($bill->bill_id);
            //            $billId    = Crypt::encrypt($bill->id);
            //            $bill->url = route('bill.pdf', $billId);
            //            $billResendArr = [
            //                'vender_name'   => $vender->name,
            //                'vender_email'  => $vender->email,
            //                'bill_name'  => $bill->name,
            //                'bill_number'   => $bill->bill,
            //                'bill_url' =>$bill->url,
            //            ];
            //
            //            $resp = Utility::sendEmailTemplate('bill_resend', [$vender->id => $vender->email], $billResendArr);
            //
            //
            //        }
            //
            //        return redirect()->back()->with('success', __('Bill successfully sent.') . (($resp['is_success'] == false && !empty($resp['error'])) ? '<br> <span class="text-danger">' . $resp['error'] . '</span>' : ''));
            //
            return redirect()->back()->with('success', __('Bill successfully sent.'));
        } else {
            return redirect()->back()->with('error', __('Permission denied.'));
        }
    }

    public function purchase($purchase_id)
    {

        $settings = Utility::settings();
        $purchaseId   = Crypt::decrypt($purchase_id);

        $purchase  = Purchase::where('id', $purchaseId)->first();
        $data  = DB::table('settings');
        $data  = $data->where('created_by', '=', $purchase->created_by);
        $data1 = $data->get();

        foreach ($data1 as $row) {
            $settings[$row->name] = $row->value;
        }

        $vendor = $purchase->vender;

        $totalTaxPrice = 0;
        $totalQuantity = 0;
        $totalRate     = 0;
        $totalDiscount = 0;
        $taxesData     = [];
        $items         = [];

        foreach ($purchase->items as $product) {

            $item              = new \stdClass();
            $item->name        = !empty($product->product) ? $product->product->name : '';
            $item->quantity    = $product->quantity;
            $item->tax         = $product->tax;
            $item->discount    = $product->discount;
            $item->price       = $product->price;
            $item->description = $product->description;

            $totalQuantity += $item->quantity;
            $totalRate     += $item->price;
            $totalDiscount += $item->discount;

            $taxes     = Utility::tax($product->tax);
            $itemTaxes = [];
            if (!empty($item->tax)) {
                foreach ($taxes as $tax) {
                    $taxPrice      = Utility::taxRate($tax->rate, $item->price, $item->quantity, $item->discount);
                    $totalTaxPrice += $taxPrice;

                    $itemTax['name']  = $tax->name;
                    $itemTax['rate']  = $tax->rate . '%';
                    $itemTax['price'] = Utility::priceFormat($settings, $taxPrice);
                    $itemTax['tax_price'] = $taxPrice;
                    $itemTaxes[]      = $itemTax;


                    if (array_key_exists($tax->name, $taxesData)) {
                        $taxesData[$tax->name] = $taxesData[$tax->name] + $taxPrice;
                    } else {
                        $taxesData[$tax->name] = $taxPrice;
                    }
                }

                $item->itemTax = $itemTaxes;
            } else {
                $item->itemTax = [];
            }
            $items[] = $item;
        }

        $purchase->itemData      = $items;
        $purchase->totalTaxPrice = $totalTaxPrice;
        $purchase->totalQuantity = $totalQuantity;
        $purchase->totalRate     = $totalRate;
        $purchase->totalDiscount = $totalDiscount;
        $purchase->taxesData     = $taxesData;


        //        $logo         = asset(Storage::url('uploads/logo/'));
        //        $company_logo = Utility::getValByName('company_logo_dark');
        //        $img          = asset($logo . '/' . (isset($company_logo) && !empty($company_logo) ? $company_logo : 'logo-dark.png'));

        $logo         = asset(Storage::url('uploads/logo/'));
        $company_logo = Utility::getValByName('company_logo_dark');
        $purchase_logo = Utility::getValByName('purchase_logo');
        if (isset($purchase_logo) && !empty($purchase_logo)) {
            $img = Utility::get_file('purchase_logo/') . $purchase_logo;
        } else {
            $img          = asset($logo . '/' . (isset($company_logo) && !empty($company_logo) ? $company_logo : 'logo-dark.png'));
        }

        if ($purchase) {
            $color      = '#' . $settings['purchase_color'];
            $font_color = Utility::getFontColor($color);

            return view('purchase.templates.' . $settings['purchase_template'], compact('purchase', 'color', 'settings', 'vendor', 'img', 'font_color'));
        } else {
            return redirect()->back()->with('error', __('Permission denied.'));
        }
    }

    public function previewPurchase($template, $color)
    {
        $objUser  = \Auth::user();
        $settings = Utility::settings();
        $purchase     = new Purchase();

        $vendor                   = new \stdClass();
        $vendor->email            = '<Email>';
        $vendor->shipping_name    = '<Vendor Name>';
        $vendor->shipping_country = '<Country>';
        $vendor->shipping_state   = '<State>';
        $vendor->shipping_city    = '<City>';
        $vendor->shipping_phone   = '<Vendor Phone Number>';
        $vendor->shipping_zip     = '<Zip>';
        $vendor->shipping_address = '<Address>';
        $vendor->billing_name     = '<Vendor Name>';
        $vendor->billing_country  = '<Country>';
        $vendor->billing_state    = '<State>';
        $vendor->billing_city     = '<City>';
        $vendor->billing_phone    = '<Vendor Phone Number>';
        $vendor->billing_zip      = '<Zip>';
        $vendor->billing_address  = '<Address>';

        $totalTaxPrice = 0;
        $taxesData     = [];
        $items         = [];
        for ($i = 1; $i <= 3; $i++) {
            $item           = new \stdClass();
            $item->name     = 'Item ' . $i;
            $item->quantity = 1;
            $item->tax      = 5;
            $item->discount = 50;
            $item->price    = 100;

            $taxes = [
                'Tax 1',
                'Tax 2',
            ];

            $itemTaxes = [];
            foreach ($taxes as $k => $tax) {
                $taxPrice         = 10;
                $totalTaxPrice    += $taxPrice;
                $itemTax['name']  = 'Tax ' . $k;
                $itemTax['rate']  = '10 %';
                $itemTax['price'] = '$10';
                $itemTax['tax_price'] = 10;
                $itemTaxes[]      = $itemTax;
                if (array_key_exists('Tax ' . $k, $taxesData)) {
                    $taxesData['Tax ' . $k] = $taxesData['Tax 1'] + $taxPrice;
                } else {
                    $taxesData['Tax ' . $k] = $taxPrice;
                }
            }
            $item->itemTax = $itemTaxes;
            $items[]       = $item;
        }

        $purchase->purchase_id    = 1;
        $purchase->issue_date = date('Y-m-d H:i:s');
        //        $purchase->due_date   = date('Y-m-d H:i:s');
        $purchase->itemData   = $items;

        $purchase->totalTaxPrice = 60;
        $purchase->totalQuantity = 3;
        $purchase->totalRate     = 300;
        $purchase->totalDiscount = 10;
        $purchase->taxesData     = $taxesData;
        $purchase->created_by     = $objUser->creatorId();

        $preview      = 1;
        $color        = '#' . $color;
        $font_color   = Utility::getFontColor($color);

        $logo         = asset(Storage::url('uploads/logo/'));
        $company_logo = Utility::getValByName('company_logo_dark');
        $settings_data = \App\Models\Utility::settingsById($purchase->created_by);
        $purchase_logo = $settings_data['purchase_logo'];

        if (isset($purchase_logo) && !empty($purchase_logo)) {
            $img = Utility::get_file('purchase_logo/') . $purchase_logo;
        } else {
            $img          = asset($logo . '/' . (isset($company_logo) && !empty($company_logo) ? $company_logo : 'logo-dark.png'));
        }


        return view('purchase.templates.' . $template, compact('purchase', 'preview', 'color', 'img', 'settings', 'vendor', 'font_color'));
    }

    public function savePurchaseTemplateSettings(Request $request)
    {

        $post = $request->all();
        unset($post['_token']);

        if (isset($post['purchase_template']) && (!isset($post['purchase_color']) || empty($post['purchase_color']))) {
            $post['purchase_color'] = "ffffff";
        }


        if ($request->purchase_logo) {
            $dir = 'purchase_logo/';
            $purchase_logo = \Auth::user()->id . '_purchase_logo.png';
            $validation = [
                'mimes:' . 'png,jpg,jpeg,gif,bmp,webp',
                'max:' . '102400',
            ];
            $path = Utility::upload_file($request, 'purchase_logo', $purchase_logo, $dir, $validation);
            if ($path['flag'] == 0) {
                return redirect()->back()->with('error', __($path['msg']));
            }
            $post['purchase_logo'] = $purchase_logo;
        }


        foreach ($post as $key => $data) {
            \DB::insert(
                'insert into settings (`value`, `name`,`created_by`) values (?, ?, ?) ON DUPLICATE KEY UPDATE `value` = VALUES(`value`) ',
                [
                    $data,
                    $key,
                    \Auth::user()->creatorId(),
                ]
            );
        }

        return redirect()->back()->with('success', __('Purchase Setting updated successfully'));
    }

    public function items(Request $request)
    {

        $items = PurchaseProduct::where('purchase_id', $request->purchase_id)->where('product_id', $request->product_id)->first();

        return json_encode($items);
    }

    public function purchaseLink($purchaseId)
    {
        try {
            $id       = Crypt::decrypt($purchaseId);
            $purchase = Purchase::findOrFail($id);
        } catch (\Exception $e) {
            return redirect()->back()->with('error', __('Purchase Not Found.'));
        }

        if (!empty($purchase)) {
            $user_id        = $purchase->created_by;
            $user           = User::find($user_id);
            $purchasePayment = PurchasePayment::where('purchase_id', $purchase->id)->first();
            $vendor = $purchase->vender;
            $iteams   = $purchase->items;

            return view('purchase.customer_bill', compact('purchase', 'vendor', 'iteams', 'purchasePayment', 'user'));
        } else {
            return redirect()->back()->with('error', __('Permission Denied.'));
        }
    }

    public function payment($purchase_id)
    {
        if (\Auth::user()->can('create payment purchase')) {
            $purchase    = Purchase::where('id', $purchase_id)->first();
            $venders = Vender::where('created_by', '=', \Auth::user()->creatorId())->get()->pluck('name', 'id');

            $categories = ProductServiceCategory::where('created_by', '=', \Auth::user()->creatorId())->get()->pluck('name', 'id');
            $accounts   = BankAccount::select('*', \DB::raw("CONCAT(bank_name,' ',holder_name) AS name"))->where('created_by', \Auth::user()->creatorId())->get()->pluck('name', 'id');

            return view('purchase.payment', compact('venders', 'categories', 'accounts', 'purchase'));
        } else {
            return redirect()->back()->with('error', __('Permission denied.'));
        }
    }

    public function createPayment(Request $request, $purchase_id)
    {
        if (\Auth::user()->can('create payment purchase')) {
            $validator = \Validator::make(
                $request->all(),
                [
                    'date' => 'required',
                    'amount' => 'required',
                    'account_id' => 'required',

                ]
            );
            if ($validator->fails()) {
                $messages = $validator->getMessageBag();

                return redirect()->back()->with('error', $messages->first());
            }

            $purchasePayment                 = new PurchasePayment();
            $purchasePayment->purchase_id        = $purchase_id;
            $purchasePayment->date           = $request->date;
            $purchasePayment->amount         = $request->amount;
            $purchasePayment->account_id     = $request->account_id;
            $purchasePayment->payment_method = 0;
            $purchasePayment->reference      = $request->reference;
            $purchasePayment->description    = $request->description;
            if (!empty($request->add_receipt)) {
                $fileName = time() . "_" . $request->add_receipt->getClientOriginalName();
                $request->add_receipt->storeAs('uploads/payment', $fileName);
                $purchasePayment->add_receipt = $fileName;
            }
            $purchasePayment->save();

            $purchase  = Purchase::where('id', $purchase_id)->first();
            $due   = $purchase->getDue();
            $total = $purchase->getTotal();

            if ($purchase->status == 0) {
                $purchase->send_date = date('Y-m-d');
                $purchase->save();
            }

            if ($due <= 0) {
                $purchase->status = 4;
                $purchase->save();
            } else {
                $purchase->status = 3;
                $purchase->save();
            }
            $purchasePayment->user_id    = $purchase->vender_id;
            $purchasePayment->user_type  = 'Vender';
            $purchasePayment->type       = 'Partial';
            $purchasePayment->created_by = \Auth::user()->id;
            $purchasePayment->payment_id = $purchasePayment->id;
            $purchasePayment->category   = 'Bill';
            $purchasePayment->account    = $request->account_id;
            Transaction::addTransaction($purchasePayment);

            $vender = Vender::where('id', $purchase->vender_id)->first();

            $payment         = new PurchasePayment();
            $payment->name   = $vender['name'];
            $payment->method = '-';
            $payment->date   = \Auth::user()->dateFormat($request->date);
            $payment->amount = \Auth::user()->priceFormat($request->amount);
            $payment->bill   = 'bill ' . \Auth::user()->purchaseNumberFormat($purchasePayment->purchase_id);

            Utility::userBalance('vendor', $purchase->vender_id, $request->amount, 'debit');

            Utility::bankAccountBalance($request->account_id, $request->amount, 'debit');

            // Send Email
            $setings = Utility::settings();
            if ($setings['new_bill_payment'] == 1) {

                $vender = Vender::where('id', $purchase->vender_id)->first();
                $billPaymentArr = [
                    'vender_name'   => $vender->name,
                    'vender_email'  => $vender->email,
                    'payment_name'  => $payment->name,
                    'payment_amount' => $payment->amount,
                    'payment_bill'  => $payment->bill,
                    'payment_date'  => $payment->date,
                    'payment_method' => $payment->method,
                    'company_name' => $payment->method,

                ];


                $resp = Utility::sendEmailTemplate('new_bill_payment', [$vender->id => $vender->email], $billPaymentArr);

                return redirect()->back()->with('success', __('Payment successfully added.') . (($resp['is_success'] == false && !empty($resp['error'])) ? '<br> <span class="text-danger">' . $resp['error'] . '</span>' : ''));
            }

            return redirect()->back()->with('success', __('Payment successfully added.'));
        }
    }

    public function paymentDestroy(Request $request, $purchase_id, $payment_id)
    {

        if (\Auth::user()->can('delete payment purchase')) {
            $payment = PurchasePayment::find($payment_id);
            PurchasePayment::where('id', '=', $payment_id)->delete();

            $purchase = Purchase::where('id', $purchase_id)->first();

            $due   = $purchase->getDue();
            $total = $purchase->getTotal();

            if ($due > 0 && $total != $due) {
                $purchase->status = 3;
            } else {
                $purchase->status = 2;
            }

            Utility::userBalance('vendor', $purchase->vender_id, $payment->amount, 'credit');
            Utility::bankAccountBalance($payment->account_id, $payment->amount, 'credit');

            $purchase->save();
            $type = 'Partial';
            $user = 'Vender';
            Transaction::destroyTransaction($payment_id, $type, $user);

            return redirect()->back()->with('success', __('Payment successfully deleted.'));
        } else {
            return redirect()->back()->with('error', __('Permission denied.'));
        }
    }

    public function vender(Request $request)
    {
        $vender = Vender::where('id', '=', $request->id)->first();

        return view('purchase.vender_detail', compact('vender'));
    }
    public function product(Request $request)
    {
        $data['product']     = $product = ProductService::find($request->product_id);
        $data['unit']        = !empty($product->unit) ? $product->unit->name : '';
        $data['taxRate']     = $taxRate = !empty($product->tax_id) ? $product->taxRate($product->tax_id) : 0;
        $data['taxes']       = !empty($product->tax_id) ? $product->tax($product->tax_id) : 0;
        $salePrice           = $product->purchase_price ?? 0;
        $quantity            = 1;
        $taxPrice            = ($taxRate / 100) * ($salePrice * $quantity);
        $data['totalAmount'] = ($salePrice * $quantity);

        return json_encode($data);
    }

    public function productDestroy(Request $request)
    {

        if (\Auth::user()->can('delete purchase')) {

            $res = PurchaseProduct::where('id', '=', $request->id)->first();
            //            $res1 = PurchaseProduct::where('purchase_id', '=', $res->purchase_id)->where('product_id', '=', $res->product_id)->get();

            $purchase = Purchase::where('created_by', '=', \Auth::user()->creatorId())->first();
            $warehouse_id = $purchase->warehouse_id;

            $ware_pro = WarehouseProduct::where('warehouse_id', $warehouse_id)->where('product_id', $res->product_id)->first();

            $qty = $ware_pro->quantity;

            if ($res->quantity == $qty || $res->quantity > $qty) {
                $ware_pro->delete();
            } elseif ($res->quantity < $qty) {
                $ware_pro->quantity =  $qty - $res->quantity;
                $ware_pro->save();
            }
            PurchaseProduct::where('id', '=', $request->id)->delete();


            return response()->json(['status' => true, 'message' => __('Purchase product successfully deleted.')]);
        } else {
            return response()->json(['status' => false, 'message' => __('Permission denied.')]);
        }
    }

    public function downloadInvoice(Purchase $purchase)
    {
        if (empty($purchase->invoice_image)) {
            return redirect()->back()->with('error', 'No invoice file found.');
        }

        $fileName = basename($purchase->invoice_image);
        $filePath = storage_path('app/uploads/purchase/' . $fileName);

        if (file_exists($filePath)) {
            return response()->file($filePath, [
                'Content-Disposition' => 'attachment; filename="' . $fileName . '"'
            ]);
        }

        return redirect()->back()->with('error', 'File not found in storage.');
    }

    /**
     * Upload invoice image for a purchase
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function uploadInvoice(Request $request, $id)
    {
        $purchase = Purchase::find($id);

        if (!$purchase || $purchase->created_by != \Auth::user()->creatorId()) {
            return redirect()->back()->with('error', __('Permission denied or purchase not found.'));
        }

        $validator = \Validator::make(
            $request->all(),
            [
                'invoice_image' => 'required|file|mimes:pdf,jpeg,png,jpg,docx,doc,gif,bmp,tiff,webp|max:102400',
            ]
        );

        if ($validator->fails()) {
            $messages = $validator->getMessageBag();
            return redirect()->back()->with('error', $messages->first());
        }

        // Delete old file if exists
        if (!empty($purchase->invoice_image)) {
            $oldFilePath = storage_path('app/uploads/purchase/' . $purchase->invoice_image);
            if (file_exists($oldFilePath)) {
                unlink($oldFilePath);
            }
        }

        // Upload new file
        $file = $request->file('invoice_image');
        $fileName = time() . "_" . $file->getClientOriginalName();
        $file->storeAs('uploads/purchase', $fileName);

        // Update purchase record
        $purchase->invoice_image = $fileName;
        $purchase->save();

        return redirect()->back()->with('success', __('Invoice uploaded successfully.'));
    }

    function billNumber()
    {
        $latest = Bill::where('created_by', '=', \Auth::user()->creatorId())->latest()->first();
        if(!$latest)
        {
            return 1;
        }

        return $latest->bill_id + 1;
    }
}
