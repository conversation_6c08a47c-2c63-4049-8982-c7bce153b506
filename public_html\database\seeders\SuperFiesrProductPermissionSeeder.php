<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class SuperFiesrProductPermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        try {
            // إنشاء الصلاحيات إذا لم تكن موجودة
            $productPermissions = [
                [
                    "name" => "manage product & service",
                    "guard_name" => "web",
                    "created_at" => now(),
                    "updated_at" => now(),
                ],
                [
                    "name" => "create product & service",
                    "guard_name" => "web",
                    "created_at" => now(),
                    "updated_at" => now(),
                ],
                [
                    "name" => "edit product & service",
                    "guard_name" => "web",
                    "created_at" => now(),
                    "updated_at" => now(),
                ],
                [
                    "name" => "delete product & service",
                    "guard_name" => "web",
                    "created_at" => now(),
                    "updated_at" => now(),
                ],
                [
                    "name" => "show product & service",
                    "guard_name" => "web",
                    "created_at" => now(),
                    "updated_at" => now(),
                ],
            ];

            // إدراج الصلاحيات إذا لم تكن موجودة
            foreach ($productPermissions as $permission) {
                Permission::firstOrCreate(
                    ['name' => $permission['name'], 'guard_name' => $permission['guard_name']],
                    $permission
                );
            }

            // البحث عن دور SUPER FIESR
            $superFiesrRole = Role::where('name', 'SUPER FIESR')->first();
            
            if (!$superFiesrRole) {
                // إنشاء دور SUPER FIESR إذا لم يكن موجوداً
                $superFiesrRole = Role::create([
                    'name' => 'SUPER FIESR',
                    'guard_name' => 'web',
                    'created_by' => 0,
                ]);
                $this->command->info('تم إنشاء دور SUPER FIESR.');
            }

            // تحديد الصلاحيات للدور
            $superFiesrPermissions = [
                'manage product & service',
                'create product & service',
                'edit product & service',
                'delete product & service',
                'show product & service',
                'show product expiry',
                'edit product expiry',
                'manage warehouse',
                'show warehouse',
                'create warehouse',
                'edit warehouse',
                'delete warehouse',
            ];

            // إعطاء الصلاحيات للدور
            foreach ($superFiesrPermissions as $permissionName) {
                $permission = Permission::where('name', $permissionName)->first();
                if ($permission && !$superFiesrRole->hasPermissionTo($permission)) {
                    $superFiesrRole->givePermissionTo($permission);
                }
            }

            $this->command->info('تم إضافة صلاحيات إدارة المنتجات لدور SUPER FIESR بنجاح.');

        } catch (\Exception $e) {
            $this->command->error('خطأ في إضافة الصلاحيات: ' . $e->getMessage());
        }
    }
}
