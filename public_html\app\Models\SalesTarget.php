<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SalesTarget extends Model
{
    protected $fillable = [
        'warehouse_id',
        'target_date',
        'daily_target',
        'weekly_target',
        'monthly_target',
        'yearly_target',
        'created_by',
    ];

    protected $casts = [
        'target_date' => 'date',
        'daily_target' => 'decimal:2',
        'weekly_target' => 'decimal:2',
        'monthly_target' => 'decimal:2',
        'yearly_target' => 'decimal:2',
    ];

    /**
     * العلاقة مع المستودع
     */
    public function warehouse(): BelongsTo
    {
        return $this->belongsTo(\App\Models\warehouse::class);
    }

    /**
     * العلاقة مع المستخدم المنشئ
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * الحصول على الهدف حسب نوع الفترة
     */
    public function getTargetByPeriod(string $period): float
    {
        return match($period) {
            'daily' => $this->daily_target,
            'weekly' => $this->weekly_target,
            'monthly' => $this->monthly_target,
            'yearly' => $this->yearly_target,
            default => 0,
        };
    }

    /**
     * فلترة الأهداف حسب المستودع
     */
    public function scopeForWarehouse($query, $warehouseId)
    {
        if ($warehouseId) {
            return $query->where('warehouse_id', $warehouseId);
        }
        return $query;
    }

    /**
     * فلترة الأهداف حسب التاريخ
     */
    public function scopeForDate($query, $date)
    {
        return $query->where('target_date', $date);
    }

    /**
     * فلترة الأهداف حسب المنشئ
     */
    public function scopeForCreator($query, $creatorId)
    {
        return $query->where('created_by', $creatorId);
    }
}
