<?php

namespace App\Http\Controllers;

use App\Models\ReceiptOrder;
use App\Models\warehouse;
use App\Models\Vender;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class ReceiptOrderController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        try {
            // جلب أوامر الاستلام مع العلاقات
            $receiptOrders = collect();
            
            // محاولة جلب البيانات من قاعدة البيانات
            try {
                if (schema()->hasTable('receipt_orders')) {
                    $receiptOrders = ReceiptOrder::with(['vendor', 'warehouse', 'fromWarehouse', 'creator'])
                        ->orderBy('created_at', 'desc')
                        ->get();
                }
            } catch (\Exception $e) {
                // في حالة عدم وجود الجدول، إنشاء مجموعة فارغة
                $receiptOrders = collect();
            }
            
            // تحويل البيانات للعرض
            $formattedOrders = $receiptOrders->map(function ($order) {
                return [
                    'id' => $order->id ?? 0,
                    'reference_number' => $order->order_number ?? 'غير محدد',
                    'type' => $order->order_type ?? 'غير محدد',
                    'vendor_name' => $order->vendor->name ?? 'غير محدد',
                    'warehouse_name' => $order->warehouse->name ?? 'غير محدد',
                    'from_warehouse_name' => $order->fromWarehouse->name ?? null,
                    'creator_name' => $order->creator->name ?? 'غير محدد',
                    'total_products' => $order->total_products ?? 0,
                    'date' => $order->invoice_date ?? $order->created_at,
                    'created_at' => $order->created_at ?? now(),
                ];
            });
            
            // جلب المستودعات للفلتر
            $warehouses = collect();
            try {
                if (schema()->hasTable('warehouses')) {
                    $warehouses = warehouse::all();
                }
            } catch (\Exception $e) {
                $warehouses = collect();
            }
            
            return view('receipt_order.index', compact('receiptOrders', 'warehouses'))
                ->with('formattedOrders', $formattedOrders);
                
        } catch (\Exception $e) {
            // في حالة حدوث خطأ، عرض صفحة فارغة مع رسالة
            return view('receipt_order.index')
                ->with('receiptOrders', collect())
                ->with('warehouses', collect())
                ->with('error', 'حدث خطأ في تحميل البيانات: ' . $e->getMessage());
        }
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        try {
            $warehouses = collect();
            $vendors = collect();
            
            try {
                if (schema()->hasTable('warehouses')) {
                    $warehouses = warehouse::all();
                }
                if (schema()->hasTable('venders')) {
                    $vendors = Vender::all();
                }
            } catch (\Exception $e) {
                // تجاهل الخطأ واستخدام مجموعات فارغة
            }
            
            return view('receipt_order.create', compact('warehouses', 'vendors'));
            
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'حدث خطأ في تحميل صفحة الإنشاء: ' . $e->getMessage());
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            // التحقق من وجود الجدول
            if (!schema()->hasTable('receipt_orders')) {
                return redirect()->back()->with('error', 'جدول أوامر الاستلام غير موجود في قاعدة البيانات');
            }
            
            $validated = $request->validate([
                'order_type' => 'required|in:استلام بضاعة,نقل بضاعة,أمر إخراج',
                'warehouse_id' => 'required|exists:warehouses,id',
                'vendor_id' => 'nullable|exists:venders,id',
                'from_warehouse_id' => 'nullable|exists:warehouses,id',
                'notes' => 'nullable|string',
            ]);
            
            $validated['created_by'] = Auth::id();
            $validated['order_number'] = 'RO-' . date('Y') . '-' . str_pad(ReceiptOrder::count() + 1, 6, '0', STR_PAD_LEFT);
            
            $receiptOrder = ReceiptOrder::create($validated);
            
            return redirect()->route('receipt-order.index')->with('success', 'تم إنشاء أمر الاستلام بنجاح');
            
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'حدث خطأ في حفظ البيانات: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified resource.
     */
    public function show($id)
    {
        try {
            if (!schema()->hasTable('receipt_orders')) {
                return redirect()->back()->with('error', 'جدول أوامر الاستلام غير موجود');
            }
            
            $receiptOrder = ReceiptOrder::with(['vendor', 'warehouse', 'fromWarehouse', 'creator'])
                ->findOrFail($id);
            
            return view('receipt_order.show', compact('receiptOrder'));
            
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'حدث خطأ في عرض البيانات: ' . $e->getMessage());
        }
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit($id)
    {
        try {
            if (!schema()->hasTable('receipt_orders')) {
                return redirect()->back()->with('error', 'جدول أوامر الاستلام غير موجود');
            }
            
            $receiptOrder = ReceiptOrder::findOrFail($id);
            $warehouses = warehouse::all();
            $vendors = Vender::all();
            
            return view('receipt_order.edit', compact('receiptOrder', 'warehouses', 'vendors'));
            
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'حدث خطأ في تحميل صفحة التعديل: ' . $e->getMessage());
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, $id)
    {
        try {
            if (!schema()->hasTable('receipt_orders')) {
                return redirect()->back()->with('error', 'جدول أوامر الاستلام غير موجود');
            }
            
            $receiptOrder = ReceiptOrder::findOrFail($id);
            
            $validated = $request->validate([
                'order_type' => 'required|in:استلام بضاعة,نقل بضاعة,أمر إخراج',
                'warehouse_id' => 'required|exists:warehouses,id',
                'vendor_id' => 'nullable|exists:venders,id',
                'from_warehouse_id' => 'nullable|exists:warehouses,id',
                'notes' => 'nullable|string',
            ]);
            
            $receiptOrder->update($validated);
            
            return redirect()->route('receipt-order.index')->with('success', 'تم تحديث أمر الاستلام بنجاح');
            
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'حدث خطأ في تحديث البيانات: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($id)
    {
        try {
            if (!schema()->hasTable('receipt_orders')) {
                return redirect()->back()->with('error', 'جدول أوامر الاستلام غير موجود');
            }
            
            $receiptOrder = ReceiptOrder::findOrFail($id);
            $receiptOrder->delete();
            
            return redirect()->route('receipt-order.index')->with('success', 'تم حذف أمر الاستلام بنجاح');
            
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'حدث خطأ في حذف البيانات: ' . $e->getMessage());
        }
    }
}
