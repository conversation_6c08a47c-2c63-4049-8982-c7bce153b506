<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use App\Models\Pos;
use App\Models\PosV2;
use App\Models\PosPayment;
use App\Models\PosProduct;
use App\Models\Customer;
use App\Models\User;
use App\Models\warehouse;
use App\Models\ProductService;
use App\Models\WarehouseProduct;
use App\Models\SalesTarget;
use App\Models\DailySalesSummary;
use App\Models\CustomerSegment;
use App\Models\ProductPerformance;
use App\Models\AutomatedInsight;
use Carbon\Carbon;

class SalesAnalyticsController extends Controller
{
    /**
     * عرض الصفحة الرئيسية لتحليل المبيعات
     */
    public function index(Request $request)
    {
        // التحقق من الصلاحيات
        if (!Auth::user()->can('show financial record')) {
            return redirect()->back()->with('error', __('You are not authorized to perform this action'));
        }

        // جلب البيانات الأساسية للعرض
        $warehouses = warehouse::where('created_by', Auth::user()->creatorId())->get();
        $currentYear = Carbon::now()->year;
        $years = range($currentYear - 5, $currentYear + 1);

        // جلب بيانات المبيعات مباشرة مثل invoice-processor
        $warehouseId = $request->get('warehouse_id');
        $date = $request->get('date', Carbon::now()->format('Y-m-d'));
        $realtimeData = $this->getRealtimeDashboardData($warehouseId, $date);

        // جلب الرؤى غير المقروءة (مع التحقق من وجود الجدول)
        $unreadInsights = collect(); // مجموعة فارغة مؤقتاً

        try {
            if (Schema::hasTable('automated_insights')) {
                $unreadInsights = AutomatedInsight::unread()
                    ->forCreator(Auth::user()->creatorId())
                    ->active()
                    ->byPriority()
                    ->latest()
                    ->limit(5)
                    ->get();
            }
        } catch (\Exception $e) {
            // في حالة عدم وجود الجدول، نستخدم مجموعة فارغة
            $unreadInsights = collect();
        }

        return view('financial_operations.sales_analytics.index', compact(
            'warehouses',
            'years',
            'currentYear',
            'unreadInsights',
            'realtimeData'
        ));
    }

    /**
     * جلب بيانات لوحة المبيعات المباشرة
     */
    public function getRealtimeDashboard(Request $request)
    {
        try {
            $creatorId = Auth::user()->creatorId();
            $warehouseId = $request->get('warehouse_id');
            $date = $request->get('date', Carbon::now()->format('Y-m-d'));

            // تشخيص البيانات أولاً
            $debugInfo = [
                'creator_id' => $creatorId,
                'warehouse_id' => $warehouseId,
                'date' => $date,
                'pos_classic_count' => 0,
                'pos_v2_count' => 0,
                'pos_classic_payments' => 0,
                'pos_v2_payments' => 0
            ];

            // مبيعات اليوم من POS Classic
            $todaySalesQuery = Pos::where('created_by', $creatorId)
                ->whereDate('pos_date', $date);

            if ($warehouseId) {
                $todaySalesQuery->where('warehouse_id', $warehouseId);
            }

            $todaySales = $todaySalesQuery->count();
            $debugInfo['pos_classic_count'] = $todaySales;

            // إجمالي مبلغ اليوم من POS Classic - طريقة محسنة
            $todayAmount = 0;
            if ($todaySales > 0) {
                $todayAmount = DB::table('pos')
                    ->join('pos_payments', 'pos.id', '=', 'pos_payments.pos_id')
                    ->where('pos.created_by', $creatorId)
                    ->whereDate('pos.pos_date', $date)
                    ->when($warehouseId, function($q) use ($warehouseId) {
                        return $q->where('pos.warehouse_id', $warehouseId);
                    })
                    ->sum('pos_payments.amount');
            }
            $debugInfo['pos_classic_payments'] = $todayAmount;

            // مبيعات اليوم من POS V2
            $todaySalesV2 = 0;
            $todayAmountV2 = 0;

            // التحقق من وجود جدول pos_v2
            if (Schema::hasTable('pos_v2')) {
                $todaySalesV2Query = PosV2::where('created_by', $creatorId)
                    ->whereDate('pos_date', $date);

                if ($warehouseId) {
                    $todaySalesV2Query->where('warehouse_id', $warehouseId);
                }

                $todaySalesV2 = $todaySalesV2Query->count();

                // إجمالي مبلغ اليوم من POS V2 - طريقة محسنة
                if ($todaySalesV2 > 0 && Schema::hasTable('pos_v2_payments')) {
                    $todayAmountV2 = DB::table('pos_v2')
                        ->join('pos_v2_payments', 'pos_v2.id', '=', 'pos_v2_payments.pos_id')
                        ->where('pos_v2.created_by', $creatorId)
                        ->whereDate('pos_v2.pos_date', $date)
                        ->when($warehouseId, function($q) use ($warehouseId) {
                            return $q->where('pos_v2.warehouse_id', $warehouseId);
                        })
                        ->sum('pos_v2_payments.amount');
                }
            }

            $debugInfo['pos_v2_count'] = $todaySalesV2;
            $debugInfo['pos_v2_payments'] = $todayAmountV2;

            // دمج النتائج
            $todaySales = $todaySales + $todaySalesV2;
            $todayAmount = $todayAmount + $todayAmountV2;

            // مبيعات الساعة الحالية
            $currentHour = Carbon::now()->format('H');

            // POS Classic
            $hourSales = Pos::where('created_by', $creatorId)
                ->whereDate('pos_date', $date)
                ->whereRaw('HOUR(created_at) = ?', [$currentHour]);

            if ($warehouseId) {
                $hourSales->where('warehouse_id', $warehouseId);
            }

            $hourSalesCount = $hourSales->count();
            $hourAmount = $hourSales->join('pos_payments', 'pos.id', '=', 'pos_payments.pos_id')
                ->sum('pos_payments.amount');

            // POS V2
            $hourSalesV2 = PosV2::where('created_by', $creatorId)
                ->whereDate('pos_date', $date)
                ->whereRaw('HOUR(created_at) = ?', [$currentHour]);

            if ($warehouseId) {
                $hourSalesV2->where('warehouse_id', $warehouseId);
            }

            $hourSalesV2Count = $hourSalesV2->count();
            $hourAmountV2 = $hourSalesV2->join('pos_v2_payments', 'pos_v2.id', '=', 'pos_v2_payments.pos_id')
                ->sum('pos_v2_payments.amount');

            // دمج النتائج
            $hourSalesCount = $hourSalesCount + $hourSalesV2Count;
            $hourAmount = $hourAmount + $hourAmountV2;

            // الهدف اليومي
            $target = SalesTarget::forCreator($creatorId)
                ->forWarehouse($warehouseId)
                ->forDate($date)
                ->first();

            $dailyTarget = $target ? $target->daily_target : 0;
            $targetAchievement = $dailyTarget > 0 ? round(($todayAmount / $dailyTarget) * 100, 2) : 0;

            // مبيعات الأسبوع
            $weekStart = Carbon::parse($date)->startOfWeek();
            $weekEnd = Carbon::parse($date)->endOfWeek();

            // POS Classic
            $weekSales = Pos::where('created_by', $creatorId)
                ->whereBetween('pos_date', [$weekStart, $weekEnd]);

            if ($warehouseId) {
                $weekSales->where('warehouse_id', $warehouseId);
            }

            $weekSalesCount = $weekSales->count();
            $weekAmount = $weekSales->join('pos_payments', 'pos.id', '=', 'pos_payments.pos_id')
                ->sum('pos_payments.amount');

            // POS V2
            $weekSalesV2 = PosV2::where('created_by', $creatorId)
                ->whereBetween('pos_date', [$weekStart, $weekEnd]);

            if ($warehouseId) {
                $weekSalesV2->where('warehouse_id', $warehouseId);
            }

            $weekSalesV2Count = $weekSalesV2->count();
            $weekAmountV2 = $weekSalesV2->join('pos_v2_payments', 'pos_v2.id', '=', 'pos_v2_payments.pos_id')
                ->sum('pos_v2_payments.amount');

            // دمج النتائج
            $weekSalesCount = $weekSalesCount + $weekSalesV2Count;
            $weekAmount = $weekAmount + $weekAmountV2;

            // مبيعات الشهر
            $monthStart = Carbon::parse($date)->startOfMonth();
            $monthEnd = Carbon::parse($date)->endOfMonth();

            // POS Classic
            $monthSales = Pos::where('created_by', $creatorId)
                ->whereBetween('pos_date', [$monthStart, $monthEnd]);

            if ($warehouseId) {
                $monthSales->where('warehouse_id', $warehouseId);
            }

            $monthSalesCount = $monthSales->count();
            $monthAmount = $monthSales->join('pos_payments', 'pos.id', '=', 'pos_payments.pos_id')
                ->sum('pos_payments.amount');

            // POS V2
            $monthSalesV2 = PosV2::where('created_by', $creatorId)
                ->whereBetween('pos_date', [$monthStart, $monthEnd]);

            if ($warehouseId) {
                $monthSalesV2->where('warehouse_id', $warehouseId);
            }

            $monthSalesV2Count = $monthSalesV2->count();
            $monthAmountV2 = $monthSalesV2->join('pos_v2_payments', 'pos_v2.id', '=', 'pos_v2_payments.pos_id')
                ->sum('pos_v2_payments.amount');

            // دمج النتائج
            $monthSalesCount = $monthSalesCount + $monthSalesV2Count;
            $monthAmount = $monthAmount + $monthAmountV2;

            // مقارنة مع الأمس
            $yesterday = Carbon::parse($date)->subDay();

            // POS Classic
            $yesterdayAmount = Pos::where('created_by', $creatorId)
                ->whereDate('pos_date', $yesterday)
                ->when($warehouseId, function($q) use ($warehouseId) {
                    return $q->where('warehouse_id', $warehouseId);
                })
                ->join('pos_payments', 'pos.id', '=', 'pos_payments.pos_id')
                ->sum('pos_payments.amount');

            // POS V2
            $yesterdayAmountV2 = PosV2::where('created_by', $creatorId)
                ->whereDate('pos_date', $yesterday)
                ->when($warehouseId, function($q) use ($warehouseId) {
                    return $q->where('warehouse_id', $warehouseId);
                })
                ->join('pos_v2_payments', 'pos_v2.id', '=', 'pos_v2_payments.pos_id')
                ->sum('pos_v2_payments.amount');

            $yesterdayAmount = $yesterdayAmount + $yesterdayAmountV2;

            $dailyGrowth = $yesterdayAmount > 0 ?
                round((($todayAmount - $yesterdayAmount) / $yesterdayAmount) * 100, 2) : 0;

            // بيانات المبيعات بالساعة (آخر 24 ساعة)
            $hourlyData = [];
            for ($i = 23; $i >= 0; $i--) {
                $hour = Carbon::now()->subHours($i);

                // POS Classic
                $hourSalesData = Pos::where('created_by', $creatorId)
                    ->whereDate('pos_date', $hour->format('Y-m-d'))
                    ->whereRaw('HOUR(created_at) = ?', [$hour->format('H')])
                    ->when($warehouseId, function($q) use ($warehouseId) {
                        return $q->where('warehouse_id', $warehouseId);
                    })
                    ->join('pos_payments', 'pos.id', '=', 'pos_payments.pos_id')
                    ->sum('pos_payments.amount');

                // POS V2
                $hourSalesDataV2 = PosV2::where('created_by', $creatorId)
                    ->whereDate('pos_date', $hour->format('Y-m-d'))
                    ->whereRaw('HOUR(created_at) = ?', [$hour->format('H')])
                    ->when($warehouseId, function($q) use ($warehouseId) {
                        return $q->where('warehouse_id', $warehouseId);
                    })
                    ->join('pos_v2_payments', 'pos_v2.id', '=', 'pos_v2_payments.pos_id')
                    ->sum('pos_v2_payments.amount');

                $totalHourSales = $hourSalesData + $hourSalesDataV2;

                $hourlyData[] = [
                    'hour' => $hour->format('H:00'),
                    'amount' => round($totalHourSales, 2)
                ];
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'today' => [
                        'sales' => $todaySales,
                        'amount' => number_format($todayAmount, 2),
                        'target' => number_format($dailyTarget, 2),
                        'achievement' => $targetAchievement,
                        'growth' => $dailyGrowth
                    ],
                    'current_hour' => [
                        'sales' => $hourSalesCount,
                        'amount' => number_format($hourAmount, 2)
                    ],
                    'week' => [
                        'sales' => $weekSalesCount,
                        'amount' => number_format($weekAmount, 2)
                    ],
                    'month' => [
                        'sales' => $monthSalesCount,
                        'amount' => number_format($monthAmount, 2)
                    ],
                    'hourly_chart' => $hourlyData,
                    'last_updated' => Carbon::now()->format('H:i:s'),
                    'debug_info' => $debugInfo // معلومات التشخيص
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ في جلب بيانات المبيعات المباشرة: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * جلب بيانات تحليل العملاء
     */
    public function getCustomerAnalytics(Request $request)
    {
        try {
            $creatorId = Auth::user()->creatorId();
            $warehouseId = $request->get('warehouse_id');
            $dateFrom = $request->get('date_from', Carbon::now()->startOfMonth()->format('Y-m-d'));
            $dateTo = $request->get('date_to', Carbon::now()->endOfMonth()->format('Y-m-d'));

            // إحصائيات العملاء العامة
            $totalCustomers = Customer::where('created_by', $creatorId)->count();

            // العملاء النشطون - البحث في كلا النظامين
            $activeCustomersFromPos = Customer::where('created_by', $creatorId)
                ->whereHas('pos', function($q) use ($dateFrom, $dateTo, $warehouseId) {
                    $q->whereBetween('pos_date', [$dateFrom, $dateTo]);
                    if ($warehouseId) {
                        $q->where('warehouse_id', $warehouseId);
                    }
                })
                ->pluck('id');

            $activeCustomersFromPosV2 = Customer::where('created_by', $creatorId)
                ->whereHas('posV2', function($q) use ($dateFrom, $dateTo, $warehouseId) {
                    $q->whereBetween('pos_date', [$dateFrom, $dateTo]);
                    if ($warehouseId) {
                        $q->where('warehouse_id', $warehouseId);
                    }
                })
                ->pluck('id');

            $activeCustomerIds = $activeCustomersFromPos->merge($activeCustomersFromPosV2)->unique();
            $activeCustomers = $activeCustomerIds->count();

            // العملاء الجدد - حساب أكثر دقة
            $newCustomersFromPos = Customer::where('created_by', $creatorId)
                ->whereHas('pos', function($q) use ($dateFrom, $dateTo, $warehouseId) {
                    $q->whereBetween('pos_date', [$dateFrom, $dateTo]);
                    if ($warehouseId) {
                        $q->where('warehouse_id', $warehouseId);
                    }
                })
                ->whereDoesntHave('pos', function($q) use ($dateFrom, $warehouseId) {
                    $q->where('pos_date', '<', $dateFrom);
                    if ($warehouseId) {
                        $q->where('warehouse_id', $warehouseId);
                    }
                })
                ->pluck('id');

            $newCustomersFromPosV2 = Customer::where('created_by', $creatorId)
                ->whereHas('posV2', function($q) use ($dateFrom, $dateTo, $warehouseId) {
                    $q->whereBetween('pos_date', [$dateFrom, $dateTo]);
                    if ($warehouseId) {
                        $q->where('warehouse_id', $warehouseId);
                    }
                })
                ->whereDoesntHave('posV2', function($q) use ($dateFrom, $warehouseId) {
                    $q->where('pos_date', '<', $dateFrom);
                    if ($warehouseId) {
                        $q->where('warehouse_id', $warehouseId);
                    }
                })
                ->pluck('id');

            $newCustomerIds = $newCustomersFromPos->merge($newCustomersFromPosV2)->unique();
            $newCustomers = $newCustomerIds->count();

            // جلب أفضل العملاء من كلا النظامين وجمعهم
            $topCustomersFromPos = $this->getTopCustomersFromPos($creatorId, $dateFrom, $dateTo, $warehouseId);
            $topCustomersFromPosV2 = $this->getTopCustomersFromPosV2($creatorId, $dateFrom, $dateTo, $warehouseId);

            // دمج وترتيب العملاء
            $allTopCustomers = collect($topCustomersFromPos)->merge($topCustomersFromPosV2);

            // تجميع البيانات حسب العميل
            $groupedCustomers = $allTopCustomers->groupBy('id')->map(function($customerGroup) {
                $first = $customerGroup->first();
                return [
                    'id' => $first->id,
                    'name' => $first->name,
                    'contact' => $first->contact,
                    'email' => $first->email,
                    'total_orders' => $customerGroup->sum('total_orders'),
                    'total_spent' => $customerGroup->sum('total_spent'),
                    'avg_order_value' => $customerGroup->avg('avg_order_value'),
                    'first_purchase_date' => $customerGroup->min('first_purchase_date'),
                    'last_purchase_date' => $customerGroup->max('last_purchase_date')
                ];
            });

            $topCustomers = $groupedCustomers->sortByDesc('total_spent')->take(15)->values();

            // حساب متوسط قيمة الطلب الإجمالي
            $totalOrderValue = $topCustomers->sum('total_spent');
            $totalOrders = $topCustomers->sum('total_orders');
            $avgOrderValue = $totalOrders > 0 ? $totalOrderValue / $totalOrders : 0;

            // إحصائيات إضافية
            $customerStats = [
                'total_revenue' => $totalOrderValue,
                'avg_order_value' => round($avgOrderValue, 2),
                'avg_customer_value' => $activeCustomers > 0 ? round($totalOrderValue / $activeCustomers, 2) : 0,
                'repeat_customers' => $topCustomers->where('total_orders', '>', 1)->count()
            ];

            return response()->json([
                'success' => true,
                'data' => [
                    'total_customers' => $totalCustomers,
                    'active_customers' => $activeCustomers,
                    'inactive_customers' => $totalCustomers - $activeCustomers,
                    'new_customers' => $newCustomers,
                    'customer_stats' => $customerStats,
                    'top_customers' => $topCustomers,
                    'period' => [
                        'from' => $dateFrom,
                        'to' => $dateTo,
                        'warehouse_id' => $warehouseId
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ في جلب بيانات تحليل العملاء: ' . $e->getMessage(),
                'error_details' => $e->getTraceAsString()
            ], 500);
        }
    }

    /**
     * جلب بيانات أداء المنتجات مع تواريخ الصلاحية
     */
    public function getProductPerformance(Request $request)
    {
        try {
            $creatorId = Auth::user()->creatorId();
            $warehouseId = $request->get('warehouse_id');
            $dateFrom = $request->get('date_from', Carbon::now()->startOfMonth());
            $dateTo = $request->get('date_to', Carbon::now()->endOfMonth());

            // استعلام مبسط وسريع - أفضل 10 منتجات من POS مع تواريخ الصلاحية
            $topProducts = DB::table('pos_products as pp')
                ->join('pos as p', 'pp.pos_id', '=', 'p.id')
                ->join('product_services as ps', 'pp.product_id', '=', 'ps.id')
                ->leftJoin('product_service_categories as psc', 'ps.category_id', '=', 'psc.id')
                ->where('p.created_by', $creatorId)
                ->whereBetween('p.pos_date', [$dateFrom, $dateTo])
                ->when($warehouseId, function($q) use ($warehouseId) {
                    return $q->where('p.warehouse_id', $warehouseId);
                })
                ->select(
                    'ps.id',
                    'ps.name',
                    'ps.sku',
                    'ps.sale_price',
                    'ps.expiry_date',
                    'psc.name as category_name',
                    DB::raw('SUM(pp.quantity) as total_quantity'),
                    DB::raw('SUM(pp.price * pp.quantity) as total_revenue'),
                    DB::raw('COUNT(DISTINCT p.id) as order_count'),
                    DB::raw('CASE
                        WHEN ps.expiry_date IS NULL THEN "لا يوجد تاريخ انتهاء"
                        WHEN ps.expiry_date <= CURDATE() THEN "منتهي الصلاحية"
                        WHEN ps.expiry_date <= DATE_ADD(CURDATE(), INTERVAL 7 DAY) THEN "خطر عالي"
                        WHEN ps.expiry_date <= DATE_ADD(CURDATE(), INTERVAL 30 DAY) THEN "تحذير"
                        ELSE "صالح"
                    END as expiry_status'),
                    DB::raw('CASE
                        WHEN ps.expiry_date IS NULL THEN NULL
                        ELSE DATEDIFF(ps.expiry_date, CURDATE())
                    END as days_to_expiry')
                )
                ->groupBy('ps.id', 'ps.name', 'ps.sku', 'ps.sale_price', 'ps.expiry_date', 'psc.name')
                ->orderBy('total_revenue', 'desc')
                ->limit(10)
                ->get();

            // جلب المنتجات ذات تاريخ الصلاحية القريب (منفصل)
            $expiringProducts = DB::table('product_services as ps')
                ->leftJoin('product_service_categories as psc', 'ps.category_id', '=', 'psc.id')
                ->leftJoin('warehouse_products as wp', 'ps.id', '=', 'wp.product_id')
                ->where('ps.created_by', $creatorId)
                ->whereNotNull('ps.expiry_date')
                ->where('ps.expiry_date', '<=', Carbon::now()->addDays(30)) // المنتجات التي تنتهي خلال 30 يوم
                ->when($warehouseId, function($q) use ($warehouseId) {
                    return $q->where('wp.warehouse_id', $warehouseId);
                })
                ->select(
                    'ps.id',
                    'ps.name',
                    'ps.sku',
                    'ps.sale_price',
                    'ps.expiry_date',
                    'psc.name as category_name',
                    'wp.quantity as current_stock',
                    DB::raw('DATEDIFF(ps.expiry_date, CURDATE()) as days_to_expiry'),
                    DB::raw('CASE
                        WHEN ps.expiry_date <= CURDATE() THEN "منتهي الصلاحية"
                        WHEN ps.expiry_date <= DATE_ADD(CURDATE(), INTERVAL 7 DAY) THEN "خطر عالي"
                        WHEN ps.expiry_date <= DATE_ADD(CURDATE(), INTERVAL 15 DAY) THEN "خطر متوسط"
                        ELSE "تحذير"
                    END as risk_level')
                )
                ->orderBy('days_to_expiry', 'asc')
                ->limit(15) // أفضل 15 منتج قريب الانتهاء
                ->get();

            // إحصائيات مبسطة
            $totalRevenue = $topProducts->sum('total_revenue');

            // إحصائيات تواريخ الصلاحية
            $expiryStats = [
                'total_expiring' => $expiringProducts->count(),
                'expired' => $expiringProducts->where('days_to_expiry', '<=', 0)->count(),
                'high_risk' => $expiringProducts->where('days_to_expiry', '>', 0)->where('days_to_expiry', '<=', 7)->count(),
                'medium_risk' => $expiringProducts->where('days_to_expiry', '>', 7)->where('days_to_expiry', '<=', 15)->count(),
                'warning' => $expiringProducts->where('days_to_expiry', '>', 15)->where('days_to_expiry', '<=', 30)->count()
            ];

            return response()->json([
                'success' => true,
                'data' => [
                    'top_selling_products' => $topProducts,
                    'expiring_products' => $expiringProducts,
                    'slow_moving_products' => [], // مؤقتاً فارغ لتحسين الأداء
                    'total_products' => $topProducts->count(),
                    'products_with_sales' => $topProducts->count(),
                    'products_without_sales' => 0,
                    'sales_coverage' => 100,
                    'expiry_statistics' => $expiryStats
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ في جلب بيانات أداء المنتجات: ' . $e->getMessage()
            ], 500);
        }
    }



















    /**
     * دالة مساعدة لجلب بيانات المبيعات مباشرة (مثل invoice-processor)
     */
    private function getRealtimeDashboardData($warehouseId = null, $date = null)
    {
        try {
            $creatorId = Auth::user()->creatorId();
            $date = $date ?: Carbon::now()->format('Y-m-d');

            // جلب البيانات مباشرة من قاعدة البيانات مثل invoice-processor
            $posQuery = Pos::where('created_by', $creatorId)
                ->with(['customer', 'warehouse', 'posPayment']);

            // تطبيق فلتر المستودع إذا تم تحديده
            if ($warehouseId) {
                $posQuery->where('warehouse_id', $warehouseId);
            }

            $posPayments = $posQuery->orderBy('id', 'desc')->get();

            // حساب الإحصائيات
            $todaySales = $posPayments->where('pos_date', $date)->count();
            $todayAmount = $posPayments->where('pos_date', $date)
                ->sum(function($pos) {
                    return $pos->posPayment ? $pos->posPayment->amount : 0;
                });

            // مبيعات الساعة الحالية
            $currentHour = Carbon::now()->format('H');
            $hourSales = $posPayments->where('pos_date', $date)
                ->filter(function($pos) use ($currentHour) {
                    return Carbon::parse($pos->created_at)->format('H') == $currentHour;
                });

            $hourSalesCount = $hourSales->count();
            $hourAmount = $hourSales->sum(function($pos) {
                return $pos->posPayment ? $pos->posPayment->amount : 0;
            });

            // مبيعات الأسبوع
            $weekStart = Carbon::parse($date)->startOfWeek();
            $weekEnd = Carbon::parse($date)->endOfWeek();
            $weekSales = $posPayments->whereBetween('pos_date', [$weekStart->format('Y-m-d'), $weekEnd->format('Y-m-d')]);
            $weekSalesCount = $weekSales->count();
            $weekAmount = $weekSales->sum(function($pos) {
                return $pos->posPayment ? $pos->posPayment->amount : 0;
            });

            // مبيعات الشهر
            $monthStart = Carbon::parse($date)->startOfMonth();
            $monthEnd = Carbon::parse($date)->endOfMonth();
            $monthSales = $posPayments->whereBetween('pos_date', [$monthStart->format('Y-m-d'), $monthEnd->format('Y-m-d')]);
            $monthSalesCount = $monthSales->count();
            $monthAmount = $monthSales->sum(function($pos) {
                return $pos->posPayment ? $pos->posPayment->amount : 0;
            });

            // إنشاء بيانات الرسم البياني للساعات
            $hourlyData = [];
            for ($i = 23; $i >= 0; $i--) {
                $hour = Carbon::now()->subHours($i);
                $hourSalesData = $posPayments->filter(function($pos) use ($hour, $date) {
                    return $pos->pos_date == $date &&
                           Carbon::parse($pos->created_at)->format('H') == $hour->format('H');
                });

                $hourAmount = $hourSalesData->sum(function($pos) {
                    return $pos->posPayment ? $pos->posPayment->amount : 0;
                });

                $hourlyData[] = [
                    'hour' => $hour->format('H:00'),
                    'amount' => round($hourAmount, 2)
                ];
            }

            return [
                'today' => [
                    'sales' => $todaySales,
                    'amount' => number_format($todayAmount, 2),
                    'target' => '0.00',
                    'achievement' => 0,
                    'growth' => 0
                ],
                'current_hour' => [
                    'sales' => $hourSalesCount,
                    'amount' => number_format($hourAmount, 2)
                ],
                'week' => [
                    'sales' => $weekSalesCount,
                    'amount' => number_format($weekAmount, 2)
                ],
                'month' => [
                    'sales' => $monthSalesCount,
                    'amount' => number_format($monthAmount, 2)
                ],
                'hourly_chart' => $hourlyData,
                'last_updated' => Carbon::now()->format('H:i:s'),
                'debug_info' => [
                    'total_pos_records' => $posPayments->count(),
                    'today_records' => $todaySales,
                    'method' => 'direct_database_query',
                    'warehouse_filter' => $warehouseId ? 'مطبق' : 'غير مطبق'
                ]
            ];

        } catch (\Exception $e) {
            return [
                'today' => ['sales' => 0, 'amount' => '0.00', 'target' => '0.00', 'achievement' => 0, 'growth' => 0],
                'current_hour' => ['sales' => 0, 'amount' => '0.00'],
                'week' => ['sales' => 0, 'amount' => '0.00'],
                'month' => ['sales' => 0, 'amount' => '0.00'],
                'hourly_chart' => [],
                'last_updated' => Carbon::now()->format('H:i:s'),
                'debug_info' => ['error' => $e->getMessage()]
            ];
        }
    }

    /**
     * جلب أفضل العملاء من نظام POS Classic
     */
    private function getTopCustomersFromPos($creatorId, $dateFrom, $dateTo, $warehouseId = null)
    {
        $query = DB::table('pos')
            ->join('customers', 'pos.customer_id', '=', 'customers.id')
            ->leftJoin('pos_payments', 'pos.id', '=', 'pos_payments.pos_id')
            ->where('pos.created_by', $creatorId)
            ->whereBetween('pos.pos_date', [$dateFrom, $dateTo]);

        if ($warehouseId) {
            $query->where('pos.warehouse_id', $warehouseId);
        }

        return $query->select(
                'customers.id',
                'customers.name',
                'customers.contact',
                'customers.email',
                DB::raw('COUNT(DISTINCT pos.id) as total_orders'),
                DB::raw('COALESCE(SUM(pos_payments.amount), 0) as total_spent'),
                DB::raw('COALESCE(AVG(pos_payments.amount), 0) as avg_order_value'),
                DB::raw('MAX(pos.pos_date) as last_purchase_date'),
                DB::raw('MIN(pos.pos_date) as first_purchase_date')
            )
            ->groupBy('customers.id', 'customers.name', 'customers.contact', 'customers.email')
            ->get()
            ->toArray();
    }

    /**
     * جلب أفضل العملاء من نظام POS V2
     */
    private function getTopCustomersFromPosV2($creatorId, $dateFrom, $dateTo, $warehouseId = null)
    {
        // التحقق من وجود جدول pos_v2
        if (!Schema::hasTable('pos_v2') || !Schema::hasTable('pos_v2_payments')) {
            return [];
        }

        $query = DB::table('pos_v2')
            ->join('customers', 'pos_v2.customer_id', '=', 'customers.id')
            ->leftJoin('pos_v2_payments', 'pos_v2.id', '=', 'pos_v2_payments.pos_id')
            ->where('pos_v2.created_by', $creatorId)
            ->whereBetween('pos_v2.pos_date', [$dateFrom, $dateTo]);

        if ($warehouseId) {
            $query->where('pos_v2.warehouse_id', $warehouseId);
        }

        return $query->select(
                'customers.id',
                'customers.name',
                'customers.contact',
                'customers.email',
                DB::raw('COUNT(DISTINCT pos_v2.id) as total_orders'),
                DB::raw('COALESCE(SUM(pos_v2_payments.amount), 0) as total_spent'),
                DB::raw('COALESCE(AVG(pos_v2_payments.amount), 0) as avg_order_value'),
                DB::raw('MAX(pos_v2.pos_date) as last_purchase_date'),
                DB::raw('MIN(pos_v2.pos_date) as first_purchase_date')
            )
            ->groupBy('customers.id', 'customers.name', 'customers.contact', 'customers.email')
            ->get()
            ->toArray();
    }

    /**
     * دالة مساعدة للتحقق من البيانات الموجودة
     */
    public function debugSalesData(Request $request)
    {
        try {
            $creatorId = Auth::user()->creatorId();
            $warehouseId = $request->get('warehouse_id');

            $debugData = [
                'creator_id' => $creatorId,
                'warehouse_id' => $warehouseId,
                'tables_exist' => [
                    'pos' => Schema::hasTable('pos'),
                    'pos_v2' => Schema::hasTable('pos_v2'),
                    'pos_payments' => Schema::hasTable('pos_payments'),
                    'pos_v2_payments' => Schema::hasTable('pos_v2_payments'),
                ],
                'total_records' => [],
                'user_records' => [],
                'today_records' => [],
                'sample_data' => []
            ];

            // إجمالي السجلات
            if (Schema::hasTable('pos')) {
                $debugData['total_records']['pos'] = DB::table('pos')->count();
                $debugData['user_records']['pos'] = DB::table('pos')->where('created_by', $creatorId)->count();
                $debugData['today_records']['pos'] = DB::table('pos')
                    ->where('created_by', $creatorId)
                    ->whereDate('pos_date', Carbon::now()->format('Y-m-d'))
                    ->count();

                // عينة من البيانات
                $debugData['sample_data']['pos'] = DB::table('pos')
                    ->where('created_by', $creatorId)
                    ->orderBy('created_at', 'desc')
                    ->limit(3)
                    ->get();
            }

            if (Schema::hasTable('pos_v2')) {
                $debugData['total_records']['pos_v2'] = DB::table('pos_v2')->count();
                $debugData['user_records']['pos_v2'] = DB::table('pos_v2')->where('created_by', $creatorId)->count();
                $debugData['today_records']['pos_v2'] = DB::table('pos_v2')
                    ->where('created_by', $creatorId)
                    ->whereDate('pos_date', Carbon::now()->format('Y-m-d'))
                    ->count();

                // عينة من البيانات
                $debugData['sample_data']['pos_v2'] = DB::table('pos_v2')
                    ->where('created_by', $creatorId)
                    ->orderBy('created_at', 'desc')
                    ->limit(3)
                    ->get();
            }

            // المدفوعات
            if (Schema::hasTable('pos_payments')) {
                $debugData['total_records']['pos_payments'] = DB::table('pos_payments')->count();
                $debugData['user_records']['pos_payments'] = DB::table('pos_payments')->where('created_by', $creatorId)->count();
            }

            if (Schema::hasTable('pos_v2_payments')) {
                $debugData['total_records']['pos_v2_payments'] = DB::table('pos_v2_payments')->count();
                $debugData['user_records']['pos_v2_payments'] = DB::table('pos_v2_payments')->where('created_by', $creatorId)->count();
            }

            // المستودعات المتاحة
            $debugData['warehouses'] = DB::table('warehouses')
                ->where('created_by', $creatorId)
                ->select('id', 'name')
                ->get();

            return response()->json([
                'success' => true,
                'debug_data' => $debugData
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في التشخيص: ' . $e->getMessage(),
                'error' => $e->getTraceAsString()
            ], 500);
        }
    }

    // ===== دوال مساعدة لاتجاهات المبيعات =====

    /**
     * جلب الاتجاهات اليومية من POS Classic
     */
    private function getDailyTrendsFromPos($creatorId, $warehouseId, $dateFrom, $dateTo)
    {
        return DB::table('pos')
            ->leftJoin('pos_payments', 'pos.id', '=', 'pos_payments.pos_id')
            ->where('pos.created_by', $creatorId)
            ->whereBetween('pos.pos_date', [$dateFrom, $dateTo])
            ->when($warehouseId, function($q) use ($warehouseId) {
                return $q->where('pos.warehouse_id', $warehouseId);
            })
            ->select(
                DB::raw('DATE(pos.pos_date) as date'),
                DB::raw('COUNT(pos.id) as sales_count'),
                DB::raw('COALESCE(SUM(pos_payments.amount), 0) as total_amount')
            )
            ->groupBy(DB::raw('DATE(pos.pos_date)'))
            ->orderBy('date')
            ->get();
    }

    /**
     * جلب الاتجاهات اليومية من POS V2
     */
    private function getDailyTrendsFromPosV2($creatorId, $warehouseId, $dateFrom, $dateTo)
    {
        if (!DB::getSchemaBuilder()->hasTable('pos_v2')) {
            return collect();
        }

        return DB::table('pos_v2')
            ->leftJoin('pos_v2_payments', 'pos_v2.id', '=', 'pos_v2_payments.pos_id')
            ->where('pos_v2.created_by', $creatorId)
            ->whereBetween('pos_v2.pos_date', [$dateFrom, $dateTo])
            ->when($warehouseId, function($q) use ($warehouseId) {
                return $q->where('pos_v2.warehouse_id', $warehouseId);
            })
            ->select(
                DB::raw('DATE(pos_v2.pos_date) as date'),
                DB::raw('COUNT(pos_v2.id) as sales_count'),
                DB::raw('COALESCE(SUM(pos_v2_payments.amount), 0) as total_amount')
            )
            ->groupBy(DB::raw('DATE(pos_v2.pos_date)'))
            ->orderBy('date')
            ->get();
    }

    /**
     * دمج الاتجاهات اليومية
     */
    private function mergeDailyTrends($posData, $posV2Data)
    {
        $merged = collect();
        $allDates = $posData->pluck('date')->merge($posV2Data->pluck('date'))->unique()->sort();

        foreach ($allDates as $date) {
            $posItem = $posData->where('date', $date)->first();
            $posV2Item = $posV2Data->where('date', $date)->first();

            $merged->push([
                'period' => $date,
                'period_name' => Carbon::parse($date)->format('d/m'),
                'sales_count' => ($posItem->sales_count ?? 0) + ($posV2Item->sales_count ?? 0),
                'total_amount' => round(($posItem->total_amount ?? 0) + ($posV2Item->total_amount ?? 0), 2)
            ]);
        }

        return $merged;
    }

    /**
     * جلب بيانات المستخدمين من POS Classic
     */
    private function getUsersFromPos($creatorId, $warehouseId, $dateFrom, $dateTo)
    {
        return DB::table('pos')
            ->join('users', 'pos.created_by', '=', 'users.id')
            ->leftJoin('pos_payments', 'pos.id', '=', 'pos_payments.pos_id')
            ->where('pos.created_by', $creatorId)
            ->whereBetween('pos.pos_date', [$dateFrom, $dateTo])
            ->when($warehouseId, function($q) use ($warehouseId) {
                return $q->where('pos.warehouse_id', $warehouseId);
            })
            ->select(
                'users.id as user_id',
                'users.name as user_name',
                'users.email',
                DB::raw('CASE
                    WHEN users.type = "delivery" THEN "delivery"
                    ELSE "cashier"
                END as user_type'),
                DB::raw('COUNT(pos.id) as total_sales'),
                DB::raw('COALESCE(SUM(pos_payments.amount), 0) as total_amount'),
                DB::raw('COALESCE(AVG(pos_payments.amount), 0) as avg_sale_value'),
                DB::raw('MAX(pos.pos_date) as last_sale_date')
            )
            ->groupBy('users.id', 'users.name', 'users.email', 'users.type')
            ->get();
    }

    /**
     * جلب بيانات المستخدمين من POS V2
     */
    private function getUsersFromPosV2($creatorId, $warehouseId, $dateFrom, $dateTo)
    {
        if (!DB::getSchemaBuilder()->hasTable('pos_v2')) {
            return collect();
        }

        return DB::table('pos_v2')
            ->join('users', 'pos_v2.created_by', '=', 'users.id')
            ->leftJoin('pos_v2_payments', 'pos_v2.id', '=', 'pos_v2_payments.pos_id')
            ->where('pos_v2.created_by', $creatorId)
            ->whereBetween('pos_v2.pos_date', [$dateFrom, $dateTo])
            ->when($warehouseId, function($q) use ($warehouseId) {
                return $q->where('pos_v2.warehouse_id', $warehouseId);
            })
            ->select(
                'users.id as user_id',
                'users.name as user_name',
                'users.email',
                DB::raw('CASE
                    WHEN users.type = "delivery" THEN "delivery"
                    ELSE "cashier"
                END as user_type'),
                DB::raw('COUNT(pos_v2.id) as total_sales'),
                DB::raw('COALESCE(SUM(pos_v2_payments.amount), 0) as total_amount'),
                DB::raw('COALESCE(AVG(pos_v2_payments.amount), 0) as avg_sale_value'),
                DB::raw('MAX(pos_v2.pos_date) as last_sale_date')
            )
            ->groupBy('users.id', 'users.name', 'users.email', 'users.type')
            ->get();
    }

    /**
     * دمج بيانات المستخدمين
     */
    private function mergeUsersData($posUsers, $posV2Users)
    {
        $merged = collect();
        $allUserIds = $posUsers->pluck('user_id')->merge($posV2Users->pluck('user_id'))->unique();

        foreach ($allUserIds as $userId) {
            $posUser = $posUsers->where('user_id', $userId)->first();
            $posV2User = $posV2Users->where('user_id', $userId)->first();

            $user = $posUser ?: $posV2User;

            $merged->push([
                'user_id' => $userId,
                'user_name' => $user->user_name,
                'email' => $user->email,
                'user_type' => $user->user_type,
                'total_sales' => ($posUser->total_sales ?? 0) + ($posV2User->total_sales ?? 0),
                'total_amount' => ($posUser->total_amount ?? 0) + ($posV2User->total_amount ?? 0),
                'avg_sale_value' => (($posUser->total_amount ?? 0) + ($posV2User->total_amount ?? 0)) /
                                  max(1, ($posUser->total_sales ?? 0) + ($posV2User->total_sales ?? 0)),
                'last_sale_date' => max($posUser->last_sale_date ?? '', $posV2User->last_sale_date ?? '')
            ]);
        }

        return $merged;
    }

    /**
     * إحصائيات من POS Classic
     */
    private function getStatsFromPos($creatorId, $warehouseId, $dateFrom, $dateTo)
    {
        $stats = DB::table('pos')
            ->leftJoin('pos_payments', 'pos.id', '=', 'pos_payments.pos_id')
            ->where('pos.created_by', $creatorId)
            ->whereBetween('pos.pos_date', [$dateFrom, $dateTo])
            ->when($warehouseId, function($q) use ($warehouseId) {
                return $q->where('pos.warehouse_id', $warehouseId);
            })
            ->select(
                DB::raw('COUNT(pos.id) as total_sales'),
                DB::raw('COALESCE(SUM(pos_payments.amount), 0) as total_amount')
            )
            ->first();

        return [
            'total_sales' => $stats->total_sales ?? 0,
            'total_amount' => $stats->total_amount ?? 0
        ];
    }

    /**
     * إحصائيات من POS V2
     */
    private function getStatsFromPosV2($creatorId, $warehouseId, $dateFrom, $dateTo)
    {
        if (!DB::getSchemaBuilder()->hasTable('pos_v2')) {
            return ['total_sales' => 0, 'total_amount' => 0];
        }

        $stats = DB::table('pos_v2')
            ->leftJoin('pos_v2_payments', 'pos_v2.id', '=', 'pos_v2_payments.pos_id')
            ->where('pos_v2.created_by', $creatorId)
            ->whereBetween('pos_v2.pos_date', [$dateFrom, $dateTo])
            ->when($warehouseId, function($q) use ($warehouseId) {
                return $q->where('pos_v2.warehouse_id', $warehouseId);
            })
            ->select(
                DB::raw('COUNT(pos_v2.id) as total_sales'),
                DB::raw('COALESCE(SUM(pos_v2_payments.amount), 0) as total_amount')
            )
            ->first();

        return [
            'total_sales' => $stats->total_sales ?? 0,
            'total_amount' => $stats->total_amount ?? 0
        ];
    }

    /**
     * العثور على أكثر الأيام مبيعاً
     */
    private function findPeakDay($creatorId, $warehouseId, $dateFrom, $dateTo)
    {
        // من POS Classic
        $posData = $this->getDailyTrendsFromPos($creatorId, $warehouseId, $dateFrom, $dateTo);
        $posV2Data = $this->getDailyTrendsFromPosV2($creatorId, $warehouseId, $dateFrom, $dateTo);

        $merged = $this->mergeDailyTrends($posData, $posV2Data);
        $peakDay = $merged->sortByDesc('total_amount')->first();

        return $peakDay ? [
            'date' => $peakDay['period'],
            'amount' => $peakDay['total_amount'],
            'sales_count' => $peakDay['sales_count']
        ] : null;
    }

    /**
     * العثور على أكثر الساعات مبيعاً
     */
    private function findPeakHour($creatorId, $warehouseId, $dateFrom, $dateTo)
    {
        // تحليل الساعات من كلا النظامين
        $hourlyData = [];

        for ($hour = 0; $hour < 24; $hour++) {
            $posAmount = DB::table('pos')
                ->leftJoin('pos_payments', 'pos.id', '=', 'pos_payments.pos_id')
                ->where('pos.created_by', $creatorId)
                ->whereBetween('pos.pos_date', [$dateFrom, $dateTo])
                ->when($warehouseId, function($q) use ($warehouseId) {
                    return $q->where('pos.warehouse_id', $warehouseId);
                })
                ->whereRaw('HOUR(pos.created_at) = ?', [$hour])
                ->sum('pos_payments.amount') ?? 0;

            $posV2Amount = 0;
            if (DB::getSchemaBuilder()->hasTable('pos_v2')) {
                $posV2Amount = DB::table('pos_v2')
                    ->leftJoin('pos_v2_payments', 'pos_v2.id', '=', 'pos_v2_payments.pos_id')
                    ->where('pos_v2.created_by', $creatorId)
                    ->whereBetween('pos_v2.pos_date', [$dateFrom, $dateTo])
                    ->when($warehouseId, function($q) use ($warehouseId) {
                        return $q->where('pos_v2.warehouse_id', $warehouseId);
                    })
                    ->whereRaw('HOUR(pos_v2.created_at) = ?', [$hour])
                    ->sum('pos_v2_payments.amount') ?? 0;
            }

            $hourlyData[] = [
                'hour' => sprintf('%02d:00', $hour),
                'amount' => $posAmount + $posV2Amount
            ];
        }

        $peakHour = collect($hourlyData)->sortByDesc('amount')->first();
        return $peakHour;
    }



    // دوال مساعدة للاتجاهات الأسبوعية والشهرية (مبسطة)
    private function getWeeklyTrendsFromPos($creatorId, $warehouseId, $dateFrom, $dateTo) { return collect(); }
    private function getWeeklyTrendsFromPosV2($creatorId, $warehouseId, $dateFrom, $dateTo) { return collect(); }
    private function mergeWeeklyTrends($posData, $posV2Data) { return collect(); }
    private function getMonthlyTrendsFromPos($creatorId, $warehouseId, $dateFrom, $dateTo) { return collect(); }
    private function getMonthlyTrendsFromPosV2($creatorId, $warehouseId, $dateFrom, $dateTo) { return collect(); }
    private function mergeMonthlyTrends($posData, $posV2Data) { return collect(); }
}
