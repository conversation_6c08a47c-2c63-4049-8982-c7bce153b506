<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('pos_v2_payments', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('pos_id');
            $table->date('date')->nullable();
            $table->decimal('amount', 15, 2)->default('0.00');
            $table->decimal('discount', 15, 2)->nullable()->default('0.00');
            $table->decimal('discount_amount', 15, 2)->nullable()->default('0.00');
            $table->string('payment_type')->default('cash'); // cash, network, split
            $table->decimal('cash_amount', 15, 2)->nullable()->default(0);
            $table->decimal('network_amount', 15, 2)->nullable()->default(0);
            $table->string('transaction_number')->nullable();
            $table->unsignedBigInteger('created_by')->default('0');
            $table->timestamps();

            // Foreign key constraints
            $table->foreign('pos_id')->references('id')->on('pos_v2')->onDelete('cascade');
            $table->foreign('created_by')->references('id')->on('users')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('pos_v2_payments');
    }
};
