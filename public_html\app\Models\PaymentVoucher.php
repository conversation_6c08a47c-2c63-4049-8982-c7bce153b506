<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PaymentVoucher extends Model
{
    use HasFactory;
    protected $guarded=[];
    protected $with = ['creator','payTo'];
    protected $table="voucher_payments";

    /**
     * Get the creator of the record.
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the updater of the record.
     */
    public function updater()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    public function payTo()
    {
        return $this->belongsTo(User::class, 'pay_to_user_id');
    }

    public function shift()
    {
        return $this->belongsTo(Shift::class, 'shift_id', 'id');
    }
    public function warehouse()
    {
        return $this->belongsTo(warehouse::class,'warehouse_id','id');
    }
}
