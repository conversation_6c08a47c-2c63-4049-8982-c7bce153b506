<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Pos;
use App\Models\PosPayment;
use App\Models\PosProduct;
use Illuminate\Support\Facades\DB;

class FixUnpaidInvoices extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'pos:fix-unpaid-invoices {--dry-run : Show what would be fixed without making changes}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix unpaid invoices by creating missing payment records and handling service transactions';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('🔍 فحص الفواتير غير المدفوعة...');
        
        // فحص الفواتير بدون سجلات دفع
        $unpaidInvoices = $this->getUnpaidInvoices();
        $emptyPaymentTypes = $this->getEmptyPaymentTypes();
        
        $this->info("📊 النتائج:");
        $this->info("   - فواتير بدون سجلات دفع: " . $unpaidInvoices->count());
        $this->info("   - فواتير مع payment_type فارغ: " . $emptyPaymentTypes->count());
        
        if ($unpaidInvoices->count() === 0 && $emptyPaymentTypes->count() === 0) {
            $this->info('✅ جميع الفواتير لديها سجلات دفع صحيحة!');
            return 0;
        }
        
        if ($this->option('dry-run')) {
            $this->showDryRunResults($unpaidInvoices, $emptyPaymentTypes);
            return 0;
        }
        
        // تأكيد من المستخدم
        if (!$this->confirm('هل تريد المتابعة مع إصلاح الفواتير؟')) {
            $this->info('تم إلغاء العملية.');
            return 0;
        }
        
        $this->fixUnpaidInvoices($unpaidInvoices, $emptyPaymentTypes);
        
        return 0;
    }
    
    /**
     * الحصول على الفواتير بدون سجلات دفع
     */
    private function getUnpaidInvoices()
    {
        return Pos::leftJoin('pos_payments', 'pos.id', '=', 'pos_payments.pos_id')
            ->whereNull('pos_payments.id')
            ->select('pos.*')
            ->get();
    }
    
    /**
     * الحصول على الفواتير مع payment_type فارغ
     */
    private function getEmptyPaymentTypes()
    {
        return PosPayment::where(function($query) {
            $query->whereNull('payment_type')
                  ->orWhere('payment_type', '');
        })->get();
    }
    
    /**
     * عرض نتائج المحاكاة
     */
    private function showDryRunResults($unpaidInvoices, $emptyPaymentTypes)
    {
        $this->info('🔍 نتائج المحاكاة (لن يتم تطبيق أي تغييرات):');
        
        if ($unpaidInvoices->count() > 0) {
            $this->info("\n📝 الفواتير التي ستحصل على سجلات دفع:");
            $this->table(
                ['معرف الفاتورة', 'رقم الفاتورة', 'التاريخ', 'المبلغ المحسوب'],
                $unpaidInvoices->map(function($pos) {
                    $amount = $this->calculateInvoiceAmount($pos->id);
                    return [
                        $pos->id,
                        $pos->pos_id,
                        $pos->pos_date,
                        number_format($amount, 2) . ' ريال'
                    ];
                })->toArray()
            );
        }
        
        if ($emptyPaymentTypes->count() > 0) {
            $this->info("\n🔧 سجلات الدفع التي ستحصل على payment_type:");
            $this->table(
                ['معرف الدفع', 'معرف الفاتورة', 'المبلغ'],
                $emptyPaymentTypes->map(function($payment) {
                    return [
                        $payment->id,
                        $payment->pos_id,
                        number_format($payment->amount, 2) . ' ريال'
                    ];
                })->toArray()
            );
        }
        
        $this->info("\n💡 لتطبيق التغييرات، شغل الأمر بدون --dry-run");
    }
    
    /**
     * إصلاح الفواتير غير المدفوعة
     */
    private function fixUnpaidInvoices($unpaidInvoices, $emptyPaymentTypes)
    {
        DB::beginTransaction();
        
        try {
            $fixedCount = 0;
            
            // إصلاح الفواتير بدون سجلات دفع
            foreach ($unpaidInvoices as $pos) {
                $amount = $this->calculateInvoiceAmount($pos->id);
                
                PosPayment::create([
                    'pos_id' => $pos->id,
                    'date' => $pos->pos_date,
                    'amount' => $amount,
                    'discount' => 0,
                    'payment_type' => 'cash',
                    'cash_amount' => $amount,
                    'network_amount' => 0,
                    'created_by' => $pos->created_by,
                ]);
                
                $fixedCount++;
                $this->info("✅ تم إنشاء سجل دفع للفاتورة رقم: {$pos->pos_id}");
            }
            
            // إصلاح payment_type الفارغة
            foreach ($emptyPaymentTypes as $payment) {
                $payment->update([
                    'payment_type' => 'cash',
                    'cash_amount' => $payment->amount,
                    'network_amount' => 0,
                ]);
                
                $fixedCount++;
                $this->info("🔧 تم إصلاح payment_type للدفع رقم: {$payment->id}");
            }
            
            DB::commit();
            
            $this->info("\n🎉 تم الإصلاح بنجاح!");
            $this->info("📊 إجمالي السجلات المصلحة: {$fixedCount}");
            
            // التحقق من النتائج
            $this->verifyFix();
            
        } catch (\Exception $e) {
            DB::rollBack();
            $this->error("❌ حدث خطأ أثناء الإصلاح: " . $e->getMessage());
            return 1;
        }
    }
    
    /**
     * حساب مبلغ الفاتورة من المنتجات
     */
    private function calculateInvoiceAmount($posId)
    {
        return PosProduct::where('pos_id', $posId)
            ->sum(DB::raw('price * quantity'));
    }
    
    /**
     * التحقق من نجاح الإصلاح
     */
    private function verifyFix()
    {
        $remainingUnpaid = $this->getUnpaidInvoices()->count();
        $remainingEmpty = $this->getEmptyPaymentTypes()->count();
        
        $this->info("\n🔍 التحقق من النتائج:");
        $this->info("   - فواتير متبقية بدون دفع: {$remainingUnpaid}");
        $this->info("   - سجلات دفع متبقية بدون نوع: {$remainingEmpty}");
        
        if ($remainingUnpaid === 0 && $remainingEmpty === 0) {
            $this->info("✅ تم إصلاح جميع المشاكل بنجاح!");
        } else {
            $this->warn("⚠️ لا تزال هناك مشاكل تحتاج مراجعة يدوية.");
        }
    }
}
