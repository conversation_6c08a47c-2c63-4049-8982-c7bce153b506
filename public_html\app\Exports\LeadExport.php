<?php

namespace App\Exports;

use App\Models\Lead;
use App\Models\User;
use App\Models\LeadStage;
use App\Models\Source;
use App\Models\Pipeline;
use App\Models\Label;
use App\Models\ProductService;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

class LeadExport implements FromCollection, WithHeadings
{
    /**
    * @return \Illuminate\Support\Collection
    */
    public function collection()
    {
        $data = Lead::where('created_by', \Auth::user()->creatorId())->get();

        foreach($data as $k => $lead)
        {
            unset($lead->id,$lead->order,
            $lead->created_by,$lead->is_active , $lead->is_converted,
            $lead->created_at,$lead->updated_at);

            $user = User::find($lead->user_id);
            $pipeline = Pipeline::find($lead->pipeline_id);
            $stage = LeadStage::find($lead->stage_id);

            // Handle sources - can be string or array
            $sourceIds = $lead->sources;
            if (is_array($sourceIds)) {
                $sourceIds = $sourceIds;
            } else {
                $sourceIds = explode(',', $sourceIds ?? '');
            }
            $sources = Source::whereIn('id', array_filter($sourceIds))->get();
            $sourceName = [];

            foreach ($sources as $source) {
                $sourceName[] = $source->name;
            }

            // Handle products - can be string or array
            $productIds = $lead->products;
            if (is_array($productIds)) {
                $productIds = $productIds;
            } else {
                $productIds = explode(',', $productIds ?? '');
            }
            $products = ProductService::whereIn('id', array_filter($productIds))->get();
            $productName = [];

            foreach ($products as $product) {
                $productName[] = $product->name;
            }

            // Handle labels - can be string or array
            $labelIds = $lead->labels ?? $lead->products; // fallback to products if labels not set
            if (is_array($labelIds)) {
                $labelIds = $labelIds;
            } else {
                $labelIds = explode(',', $labelIds ?? '');
            }
            $labels = Label::whereIn('id', array_filter($labelIds))->get();
            $labelName = [];

            foreach ($labels as $label) {
                $labelName[] = $label->name;
            }

            $data[$k]["user_id"] =   !empty($user) ? $user->name : '';
            $data[$k]["pipeline_id"]     = $pipeline->name;
            $data[$k]["stage_id"]     =  $stage->name;
            $data[$k]["sources"]     = implode(',', $sourceName);
            $data[$k]["products"]     = implode(',', $productName);
            $data[$k]["labels"]     = implode(',', $labelName);

        }
        return $data;
    }

    public function headings(): array
    {
        return [
            "Name",
            "Email",
            "Contact",
            "Subject",
            "User",
            "Pipeline",
            "Lead Stage",
            "Lead Sources",
            "Products",
            "Notes",
            "Labels",
            "Date",
        ];
    }
}
