<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class SupportReply extends Model
{
    protected $fillable = [
        'support_id',
        'user',
        'description',
        'created_by',
        'is_read',
    ];

    public function users()
    {
        return $this->hasOne('App\Models\User', 'id', 'user');
    }

    /**
     * تحصل على المرفقات المرتبطة بالرد
     * يتم تحديد المرفقات بناءً على معرف التذكرة ومنشئ الرد ووقت الإنشاء
     */
    public function attachments()
    {
        return $this->hasMany('App\Models\SupportAttachment', 'support_id', 'support_id')
                    ->where('created_by', $this->created_by)
                    ->where('created_at', '>=', $this->created_at)
                    ->where('created_at', '<=', $this->created_at->addMinutes(1));
    }
}
