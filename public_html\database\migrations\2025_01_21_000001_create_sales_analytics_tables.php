<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // جدول أهداف المبيعات
        Schema::create('sales_targets', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('warehouse_id')->nullable();
            $table->date('target_date');
            $table->decimal('daily_target', 15, 2)->default(0);
            $table->decimal('weekly_target', 15, 2)->default(0);
            $table->decimal('monthly_target', 15, 2)->default(0);
            $table->decimal('yearly_target', 15, 2)->default(0);
            $table->unsignedBigInteger('created_by');
            $table->timestamps();

            $table->foreign('warehouse_id')->references('id')->on('warehouses')->onDelete('cascade');
            $table->foreign('created_by')->references('id')->on('users')->onDelete('cascade');
            $table->index(['warehouse_id', 'target_date']);
        });

        // جدول ملخص المبيعات اليومية
        Schema::create('daily_sales_summary', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->date('summary_date');
            $table->unsignedBigInteger('warehouse_id')->nullable();
            $table->integer('total_sales')->default(0);
            $table->decimal('total_amount', 15, 2)->default(0);
            $table->integer('total_customers')->default(0);
            $table->decimal('average_sale', 15, 2)->default(0);
            $table->decimal('cash_amount', 15, 2)->default(0);
            $table->decimal('network_amount', 15, 2)->default(0);
            $table->unsignedBigInteger('created_by');
            $table->timestamps();

            $table->foreign('warehouse_id')->references('id')->on('warehouses')->onDelete('cascade');
            $table->foreign('created_by')->references('id')->on('users')->onDelete('cascade');
            $table->unique(['summary_date', 'warehouse_id']);
            $table->index(['summary_date', 'warehouse_id']);
        });

        // جدول تصنيف العملاء
        Schema::create('customer_segments', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('customer_id');
            $table->enum('segment_type', ['VIP', 'Regular', 'New', 'Inactive'])->default('New');
            $table->integer('rfm_score')->default(0);
            $table->decimal('total_spent', 15, 2)->default(0);
            $table->integer('purchase_frequency')->default(0);
            $table->date('last_purchase_date')->nullable();
            $table->date('predicted_next_purchase')->nullable();
            $table->decimal('average_order_value', 15, 2)->default(0);
            $table->integer('days_since_last_purchase')->default(0);
            $table->unsignedBigInteger('created_by');
            $table->timestamps();

            $table->foreign('customer_id')->references('id')->on('customers')->onDelete('cascade');
            $table->foreign('created_by')->references('id')->on('users')->onDelete('cascade');
            $table->index(['customer_id', 'segment_type']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('customer_segments');
        Schema::dropIfExists('daily_sales_summary');
        Schema::dropIfExists('sales_targets');
    }
};
