<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddWarehouseIdsToProductServiceCategoriesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('product_service_categories', function (Blueprint $table) {
            // Add warehouse_ids field to store the warehouses this category is available in
            // Using JSON type to store multiple warehouse IDs
            $table->json('warehouse_ids')->nullable()->after('color');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('product_service_categories', function (Blueprint $table) {
            $table->dropColumn('warehouse_ids');
        });
    }
}
